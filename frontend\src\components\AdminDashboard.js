import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Container,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  School,
  VideoLibrary,
  Group,
  PersonAdd,
  Help,
  Settings,
  Logout,
  Notifications,
  CheckCircle,
  Error as ErrorIcon,
  Sync
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import toast from 'react-hot-toast';

// استيراد خدمة التكامل المختلطة Firebase + Supabase
import {
  hybridAuth,
  hybridCourses,
  hybridStudents,
  hybridEnrollments,
  hybridFAQs
} from '../services/hybridDatabaseService';
import { initializeSupabase } from '../supabase/config';

// استيراد مكونات لوحة التحكم الموجودة
import DashboardOverview from './admin/DashboardOverview';
import CourseManagement from './admin/CourseManagement';
import StudentManagement from './admin/StudentManagement';
import StudentEnrollmentManagement from './admin/StudentEnrollmentManagement';
import FAQManagement from './admin/FAQManagement';
// import AnalyticsReports from './admin/AnalyticsReports'; // تم إزالة التحليلات والإحصائيات
import AdminProfile from './admin/AdminProfile';
import SystemTestDashboard from './admin/SystemTestDashboard';
import SupabaseConnectionTest from './SupabaseConnectionTest';
import RealTimeSyncTest from './admin/RealTimeSyncTest';
import DatabaseCleanerTool from './admin/DatabaseCleanerTool';
import AddFunctionsTest from './admin/AddFunctionsTest';
import SystemSettings from './admin/SystemSettings';

// استيراد معالجات اللمس المحسنة للأجهزة اللوحية
import { useEnhancedTouch, useEnhancedButton } from '../hooks/useEnhancedTouch';

// سيتم تحديد عرض الدرج الجانبي ديناميكياً حسب نوع الجهاز

// تحسينات محسنة للأجهزة اللوحية والأجهزة اللمسية
const useTabletOptimizations = () => {
  const theme = useTheme();

  // تحديد أنواع الأجهزة بدقة أكبر
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isSmallTablet = useMediaQuery('(min-width: 768px) and (max-width: 1023px)');
  const isLargeTablet = useMediaQuery('(min-width: 1024px) and (max-width: 1366px)');
  const isDesktop = useMediaQuery('(min-width: 1367px)');

  // تحديد نوع الإدخال
  const isTouchDevice = useMediaQuery('(pointer: coarse)');
  const hasHover = useMediaQuery('(hover: hover) and (pointer: fine)');

  // تحديد الجهاز الحالي
  const deviceType = isMobile ? 'mobile' :
                    isSmallTablet ? 'smallTablet' :
                    isLargeTablet ? 'largeTablet' : 'desktop';

  return {
    // معلومات الجهاز
    isMobile,
    isSmallTablet,
    isLargeTablet,
    isDesktop,
    isTablet: isSmallTablet || isLargeTablet,
    isTouchDevice,
    hasHover,
    deviceType,

    // أحجام محسنة حسب نوع الجهاز
    buttonSize: isMobile ? 'medium' :
                isSmallTablet ? 'large' :
                isLargeTablet ? 'large' : 'medium',

    iconSize: isMobile ? '1.2rem' :
              isSmallTablet ? '1.4rem' :
              isLargeTablet ? '1.5rem' : '1.3rem',

    fontSize: isMobile ? '0.9rem' :
              isSmallTablet ? '1rem' :
              isLargeTablet ? '1.1rem' : '1rem',

    spacing: isMobile ? 2 :
             isSmallTablet ? 3 :
             isLargeTablet ? 4 : 3,

    // تحسينات اللمس حسب نوع الجهاز
    touchTarget: isMobile ? 44 :
                 isSmallTablet ? 48 :
                 isLargeTablet ? 52 :
                 isTouchDevice ? 48 : 44,

    padding: isMobile ? '8px 16px' :
             isSmallTablet ? '12px 20px' :
             isLargeTablet ? '14px 24px' : '10px 18px',

    // عرض الدرج الجانبي
    drawerWidth: isSmallTablet ? 300 :
                 isLargeTablet ? 320 : 300,

    // ارتفاع الشريط العلوي
    appBarHeight: isMobile ? 56 :
                  isSmallTablet ? 64 :
                  isLargeTablet ? 68 : 64,

    // تحسينات الأداء
    shouldUseVirtualization: isSmallTablet || isLargeTablet,
    shouldPreloadImages: !isMobile,
    shouldUseTransitions: hasHover
  };
};

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const { language, t, isRTL } = useLanguage();
  const theme = useTheme();

  // سيتم استخدام التحسينات المحسنة للأجهزة اللوحية أدناه

  // سيتم طباعة معلومات التصحيح بعد تعريف المتغيرات

  // تحسينات الأجهزة اللوحية
  const tabletOpts = useTabletOptimizations();
  const {
    isMobile,
    isSmallTablet,
    isLargeTablet,
    isDesktop,
    isTablet,
    isTouchDevice,
    hasHover,
    deviceType,
    drawerWidth,
    appBarHeight,
    touchTarget,
    shouldUseTransitions,
    isPermanentDrawer: isTabletOrDesktop,
    isTemporaryDrawer: isMobileDevice
  } = {
    ...tabletOpts,
    isPermanentDrawer: tabletOpts.isTablet || tabletOpts.isDesktop,
    isTemporaryDrawer: tabletOpts.isMobile
  };

  // معالجات اللمس المحسنة للأجهزة اللوحية
  const enhancedTouch = useEnhancedTouch({
    hapticFeedback: true,
    visualFeedback: true,
    preventDoubleClick: true,
    debounceTime: 300,
    scaleEffect: true,
    autoOptimize: true
  });

  // معالجات محسنة للأزرار
  const menuButtonTouch = useEnhancedButton(handleDrawerToggle, {
    hapticFeedback: true,
    visualFeedback: true,
    scaleEffect: true
  });

  const logoutButtonTouch = useEnhancedButton(handleLogout, {
    hapticFeedback: true,
    visualFeedback: true,
    scaleEffect: true
  });

  // طباعة معلومات التصحيح المحسنة
  console.log('🔍 معلومات الشاشة المحسنة:', {
    width: window.innerWidth,
    height: window.innerHeight,
    deviceType,
    isTabletOrDesktop,
    isMobileDevice,
    drawerWidth,
    appBarHeight,
    touchTarget,
    userAgent: navigator.userAgent,
    breakpoints: {
      xs: theme.breakpoints.values.xs,
      sm: theme.breakpoints.values.sm,
      md: theme.breakpoints.values.md,
      lg: theme.breakpoints.values.lg,
      xl: theme.breakpoints.values.xl
    }
  });

  // حالات المكون
  const [mobileOpen, setMobileOpen] = useState(false);
  const [selectedSection, setSelectedSection] = useState('dashboard');
  const [systemStatus, setSystemStatus] = useState('initializing');
  const [realtimeStatus, setRealtimeStatus] = useState('disconnected');
  const [adminData, setAdminData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تبديل الدرج الجانبي مع تحسينات للأجهزة اللوحية
  const handleDrawerToggle = useCallback(() => {
    console.log('🔄 تبديل الدرج الجانبي:', {
      deviceType,
      isTabletOrDesktop,
      isMobileDevice,
      currentState: mobileOpen
    });

    // فقط للأجهزة المحمولة
    if (isMobileDevice) {
      setMobileOpen(!mobileOpen);
    }
  }, [deviceType, isTabletOrDesktop, isMobileDevice, mobileOpen]);

  // تسجيل الخروج
  const handleLogout = useCallback(async () => {
    try {
      console.log('🚪 بدء عملية تسجيل الخروج...');

      // إيقاف جميع المراقبات
      if (window.hybridUnsubscribes) {
        window.hybridUnsubscribes.forEach(unsubscribe => {
          if (typeof unsubscribe === 'function') {
            unsubscribe();
          }
        });
        window.hybridUnsubscribes = [];
      }

      // تسجيل الخروج من Firebase
      await logout();

      console.log('✅ تم تسجيل الخروج بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تسجيل الخروج:', error);
    }
  }, [logout]);

  // قائمة عناصر القائمة الجانبية مع الترجمة
  const menuItems = [
    {
      id: 'dashboard',
      label: t('dashboard') || 'لوحة التحكم',
      icon: <Dashboard />,
      color: '#2196F3',
      description: language === 'ar' ? 'نظرة عامة على النظام' : 'System Overview'
    },
    {
      id: 'courses',
      label: t('courseManagement') || 'إدارة الكورسات',
      icon: <VideoLibrary />,
      color: '#4CAF50',
      description: language === 'ar' ? 'إضافة وإدارة الكورسات' : 'Add and manage courses'
    },
    {
      id: 'students',
      label: t('studentManagement') || 'إدارة الطلاب',
      icon: <Group />,
      color: '#FF9800',
      description: language === 'ar' ? 'إضافة وإدارة الطلاب' : 'Add and manage students'
    },
    {
      id: 'enrollments',
      label: t('enrollmentManagement') || 'إدارة التسجيلات',
      icon: <PersonAdd />,
      color: '#9C27B0',
      description: language === 'ar' ? 'تسجيل الطلاب في الكورسات' : 'Enroll students in courses'
    },
    {
      id: 'faqs',
      label: language === 'ar' ? 'الأسئلة الشائعة' : 'FAQ Management',
      icon: <Help />,
      color: '#00BCD4',
      description: language === 'ar' ? 'إدارة الأسئلة الشائعة' : 'Manage frequently asked questions'
    },
    {
      id: 'profile',
      label: t('profile') || 'الملف الشخصي',
      icon: <Settings />,
      color: '#607D8B',
      description: language === 'ar' ? 'إعدادات الملف الشخصي' : 'Profile settings'
    },
    {
      id: 'system-settings',
      label: t('systemSettings') || 'إعدادات النظام',
      icon: <Settings />,
      color: '#795548',
      description: language === 'ar' ? 'أدوات الاختبار والصيانة والتشخيص' : 'Testing, maintenance and diagnostic tools'
    }
  ];

  // تهيئة النظام عند تحميل المكون
  useEffect(() => {
    initializeSystem();
    return () => {
      // تنظيف المراقبات عند إلغاء تحميل المكون
      console.log('🧹 تنظيف مراقبات النظام...');
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // مراقبة تغييرات القسم المحدد
  useEffect(() => {
    console.log('🎯 تغيير القسم المحدد إلى:', selectedSection);
  }, [selectedSection]);

  // تهيئة النظام المختلط
  const initializeSystem = async () => {
    try {
      setLoading(true);
      setSystemStatus('initializing');

      console.log('🚀 بدء تهيئة النظام المختلط Firebase + Supabase...');

      // تهيئة Supabase
      await initializeSupabase();

      // التحقق من حالة المصادقة
      const currentUser = hybridAuth.getCurrentUser();
      if (currentUser) {
        console.log('👤 المستخدم مسجل دخول:', currentUser.email);
      }

      setSystemStatus('connected');

      // بدء مراقبة البيانات الفورية
      startRealtimeSync();

      toast.success('تم تهيئة النظام المختلط بنجاح');
      console.log('✅ تم تهيئة النظام المختلط بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تهيئة النظام:', error);
      setSystemStatus('error');
      setError('فشل في تهيئة النظام: ' + error.message);
      toast.error('فشل في تهيئة النظام');
    } finally {
      setLoading(false);
    }
  };

  // بدء المزامنة الفورية المختلطة
  const startRealtimeSync = () => {
    try {
      setRealtimeStatus('connecting');

      // مراقبة الكورسات
      const coursesUnsubscribe = hybridCourses.watchCourses((courses) => {
        setAdminData(prev => ({ ...prev, courses }));
        console.log('📚 تحديث الكورسات:', courses.length);
      });

      // مراقبة الطلاب
      const studentsUnsubscribe = hybridStudents.watchStudents((students) => {
        setAdminData(prev => ({ ...prev, students }));
        console.log('👨‍🎓 تحديث الطلاب:', students.length);
      });

      // مراقبة التسجيلات
      const enrollmentsUnsubscribe = hybridEnrollments.watchEnrollments((enrollments) => {
        setAdminData(prev => ({ ...prev, enrollments }));
        console.log('📝 تحديث التسجيلات:', enrollments.length);
      });

      // مراقبة الأسئلة الشائعة
      const faqsUnsubscribe = hybridFAQs.watchFAQs((faqs) => {
        setAdminData(prev => ({ ...prev, faqs }));
        console.log('❓ تحديث الأسئلة الشائعة:', faqs.length);
      });

      setRealtimeStatus('connected');
      console.log('🔄 تم بدء المزامنة الفورية المختلطة');

      // حفظ دوال إلغاء الاشتراك للتنظيف لاحقاً
      window.hybridUnsubscribes = [
        coursesUnsubscribe,
        studentsUnsubscribe,
        enrollmentsUnsubscribe,
        faqsUnsubscribe
      ];
    } catch (error) {
      console.error('❌ خطأ في المزامنة الفورية:', error);
      setRealtimeStatus('error');
    }
  };

  // التعامل مع تبديل القائمة الجانبية - تم دمجه مع النسخة المحسنة أعلاه

  // إغلاق Drawer على الأجهزة المحمولة فقط
  const closeDrawerOnMobile = () => {
    if (isMobile && mobileOpen) {
      console.log('📱 إغلاق Drawer تلقائياً على الجهاز المحمول');
      setMobileOpen(false);
    }
  };

  // التعامل مع تغيير القسم
  const handleSectionChange = (sectionId) => {
    console.log('🔄 تغيير القسم إلى:', sectionId);
    console.log('📊 الحالة الحالية - selectedSection:', selectedSection, 'isMobile:', isMobile, 'mobileOpen:', mobileOpen);

    setSelectedSection(sectionId);

    // إغلاق Drawer على الأجهزة المحمولة فقط
    closeDrawerOnMobile();

    console.log('✅ تم تغيير القسم إلى:', sectionId);
  };

  // التعامل مع قائمة المستخدم


  // تم دمج handleLogout مع النسخة المحسنة أعلاه

  // عرض المحتوى حسب القسم المحدد
  const renderContent = () => {
    console.log('🎯 عرض المحتوى للقسم:', selectedSection);
    if (loading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            {t('initializing') || (language === 'ar' ? 'جاري تهيئة النظام...' : 'Initializing system...')}
          </Typography>
        </Box>
      );
    }

    if (systemStatus === 'error') {
      return (
        <Alert severity="error" sx={{ m: 2 }}>
          <Typography variant="h6">
            {language === 'ar' ? 'خطأ في النظام' : 'System Error'}
          </Typography>
          <Typography>{error}</Typography>
        </Alert>
      );
    }

    switch (selectedSection) {
      case 'dashboard':
        return <DashboardOverview adminData={adminData} />;
      case 'courses':
        return <CourseManagement />;
      case 'students':
        return <StudentManagement />;
      case 'enrollments':
        return <StudentEnrollmentManagement />;
      case 'faqs':
        return <FAQManagement />;
      // case 'analytics': تم إزالة التحليلات والإحصائيات
      case 'profile':
        return <AdminProfile />;
      case 'system-settings':
        return <SystemSettings />;
      case 'system-test':
        return <SystemTestDashboard />;
      case 'supabase-test':
        return <SupabaseConnectionTest />;
      case 'realtime-sync-test':
        return <RealTimeSyncTest />;
      case 'database-cleaner':
        return <DatabaseCleanerTool />;
      case 'add-functions-test':
        return <AddFunctionsTest />;
      default:
        return <DashboardOverview adminData={adminData} />;
    }
  };

  // مؤشر حالة الاتصال
  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return '#4CAF50';
      case 'connecting': return '#FF9800';
      case 'error': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'connected':
        return t('connected') || (language === 'ar' ? 'متصل' : 'Connected');
      case 'connecting':
        return t('initializing') || (language === 'ar' ? 'جاري الاتصال' : 'Connecting');
      case 'error':
        return language === 'ar' ? 'خطأ في الاتصال' : 'Connection Error';
      default:
        return t('disconnected') || (language === 'ar' ? 'غير متصل' : 'Disconnected');
    }
  };

  // القائمة الجانبية
  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* رأس القائمة */}
      <Box
        sx={{
          p: 3,
          background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
          color: 'white',
          textAlign: 'center'
        }}
      >
        <School sx={{ fontSize: '2.5rem', mb: 1, color: '#FFD700' }} />
        <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
          SKILLS WORLD ACADEMY
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9, mt: 0.5 }}>
          {language === 'ar' ? 'لوحة تحكم المدير - الإنتاج' : 'Admin Dashboard - Production'}
        </Typography>

        {/* مؤشر حالة الاتصال */}
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Chip
            icon={realtimeStatus === 'connected' ? <CheckCircle /> :
                  realtimeStatus === 'error' ? <ErrorIcon /> : <Sync />}
            label={getStatusText(realtimeStatus)}
            size="small"
            sx={{
              backgroundColor: getStatusColor(realtimeStatus),
              color: 'white',
              fontSize: '0.75rem'
            }}
          />
        </Box>
      </Box>

      <Divider />

      {/* قائمة العناصر */}
      <List sx={{ flexGrow: 1, py: 1 }}>
        {menuItems.map((item) => (
          <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
            <ListItemButton
              selected={selectedSection === item.id}
              onClick={() => {
                console.log('👆 نقر محسن على عنصر القائمة:', item.id, item.label);
                handleSectionChange(item.id);
              }}
              className="enhanced-touch enhanced-hover enhanced-focus"
              sx={{
                // تحسينات اللمس للأجهزة اللوحية
                minHeight: isTouchDevice ? 56 : 48,
                touchAction: 'manipulation',
                cursor: 'pointer',
                userSelect: 'none',
                WebkitUserSelect: 'none',
                WebkitTapHighlightColor: 'transparent',
                WebkitTouchCallout: 'none',
                // تحسين الأداء
                willChange: 'transform, opacity',
                transform: 'translateZ(0)',
                backfaceVisibility: 'hidden',
                mx: 1,
                borderRadius: 2,
                minHeight: { xs: 48, sm: 56, md: 52 },
                padding: { xs: '8px 16px', sm: '12px 20px', md: '10px 16px' },
                // تحسينات اللمس
                touchAction: 'manipulation',
                userSelect: 'none',
                cursor: 'pointer',
                // تحسين التفاعل
                '&:active': {
                  transform: 'scale(0.98)',
                  transition: 'transform 0.1s ease'
                },
                '&.Mui-selected': {
                  backgroundColor: `${item.color}20`,
                  borderLeft: `4px solid ${item.color}`,
                  '&:hover': {
                    backgroundColor: `${item.color}30`,
                  },
                  '&:active': {
                    backgroundColor: `${item.color}40`,
                  }
                },
                '&:hover': {
                  backgroundColor: `${item.color}10`,
                  transform: 'translateX(-2px)',
                  transition: 'all 0.2s ease'
                },
              }}
            >
              <ListItemIcon
                sx={{
                  color: item.color,
                  minWidth: { xs: 40, sm: 48, md: 44 },
                  '& .MuiSvgIcon-root': {
                    fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.3rem' }
                  }
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.label}
                secondary={item.description}
                primaryTypographyProps={{
                  fontSize: { xs: '0.9rem', sm: '1rem', md: '0.95rem' },
                  fontWeight: selectedSection === item.id ? 'bold' : 'normal'
                }}
                secondaryTypographyProps={{
                  fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.75rem' },
                  color: 'text.secondary'
                }}
              />
              </ListItemButton>
            </ListItem>
        ))}
      </List>

      <Divider />

      {/* معلومات المستخدم */}
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Avatar sx={{ width: 32, height: 32, mr: 1, bgcolor: '#FFD700' }}>
            {user?.name?.charAt(0) || 'A'}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              {user?.name || (language === 'ar' ? 'المدير' : 'Admin')}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {language === 'ar' ? 'مدير النظام' : 'System Administrator'}
            </Typography>
          </Box>
        </Box>

        <ListItemButton
          {...logoutButtonTouch.buttonProps}
          sx={{
            borderRadius: 1,
            color: '#F44336',
            minHeight: { xs: 48, sm: 56, md: 52 },
            padding: { xs: '8px 16px', sm: '12px 20px', md: '10px 16px' },
            // تحسينات اللمس
            touchAction: 'manipulation',
            userSelect: 'none',
            cursor: 'pointer',
            '&:hover': {
              backgroundColor: '#F4433610',
              transform: 'translateX(-2px)',
              transition: 'all 0.2s ease'
            },
            '&:active': {
              transform: 'scale(0.98)',
              backgroundColor: '#F4433620',
              transition: 'transform 0.1s ease'
            }
          }}
        >
          <ListItemIcon
            sx={{
              color: '#F44336',
              minWidth: { xs: 40, sm: 48, md: 44 },
              '& .MuiSvgIcon-root': {
                fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.3rem' }
              }
            }}
          >
            <Logout />
          </ListItemIcon>
          <ListItemText
            primary={t('logout') || (language === 'ar' ? 'تسجيل الخروج' : 'Logout')}
            primaryTypographyProps={{
              fontSize: { xs: '0.9rem', sm: '1rem', md: '0.95rem' },
              fontWeight: 500
            }}
          />
        </ListItemButton>
      </Box>
    </Box>
  );

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        position: 'relative',
      flexDirection: 'row', // تأكيد الاتجاه الأفقي
      direction: language === 'ar' ? 'rtl' : 'ltr'
    }}
    className={`admin-dashboard ${isTablet ? 'admin-dashboard-tablet' : ''} mobile-optimized touch-optimized scroll-optimized text-optimized ${enhancedTouch.isTouchDevice ? 'enhanced-touch' : ''} ${enhancedTouch.hasHover ? 'enhanced-hover' : ''} enhanced-focus`}
    >
      {/* شريط التطبيق العلوي */}
      <AppBar
        position="fixed"
        sx={{
          width: {
            xs: '100%',
            md: `calc(100% - ${drawerWidth}px)`
          },
          backgroundColor: '#0000FF',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          zIndex: (theme) => theme.zIndex.drawer - 1,
          // تحسينات للأجهزة اللوحية والشاشات الكبيرة
          height: appBarHeight,
          // تحسين موضع الشريط للشاشات الكبيرة
          right: { xs: 0, md: 0 },
          left: { xs: 0, md: 'auto' },
          // تحسين الأداء
          willChange: 'auto',
          transform: 'translateZ(0)',
          backfaceVisibility: 'hidden',
          // تحسين التفاعل
          touchAction: 'manipulation',
          // تحسينات خاصة للأجهزة اللوحية
          transition: shouldUseTransitions ? 'all 0.3s ease' : 'none',
          ...(isTouchDevice && {
            WebkitTapHighlightColor: 'transparent',
            WebkitTouchCallout: 'none'
          }),
          '& .MuiToolbar-root': {
            minHeight: appBarHeight,
            padding: {
              xs: '0 16px',
              sm: '0 20px',
              md: '0 24px',
              lg: '0 32px'
            },
            justifyContent: 'space-between',
            touchAction: 'manipulation'
          }
        }}
      >
        <Toolbar sx={{
          direction: language === 'ar' ? 'rtl' : 'ltr',
          minHeight: appBarHeight,
          padding: {
            xs: '0 16px',
            sm: '0 20px',
            md: '0 24px',
            lg: '0 32px'
          },
          justifyContent: 'space-between',
          width: '100%',
          // تحسين التفاعل للأجهزة اللوحية
          touchAction: 'manipulation',
          // تحسين الأداء
          willChange: 'auto',
          transform: 'translateZ(0)',
          backfaceVisibility: 'hidden'
        }}>
          {/* الجانب الأيسر - زر القائمة للأجهزة المحمولة */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {/* زر القائمة - محسن للأجهزة اللوحية */}
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              {...menuButtonTouch.buttonProps}
              sx={{
                mr: 2,
                display: { md: 'none' }, // إخفاء على الأجهزة اللوحية والشاشات الكبيرة
                padding: tabletOpts.padding,
                minWidth: touchTarget,
                minHeight: touchTarget,
                // تحسينات اللمس للأجهزة اللوحية
                touchAction: 'manipulation',
                userSelect: 'none',
                cursor: 'pointer',
                // تحسين الأداء
                willChange: 'transform, opacity',
                transform: 'translateZ(0)',
                backfaceVisibility: 'hidden',
                // تحسينات للأجهزة اللمسية
                ...(isTouchDevice && {
                  WebkitTapHighlightColor: 'rgba(255, 255, 255, 0.1)',
                  WebkitTouchCallout: 'none'
                }),
                // تحسين hover للأجهزة التي تدعمها
                ...(hasHover && {
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    transform: 'scale(1.05) translateZ(0)',
                    transition: 'all 0.2s ease'
                  }
                }),
                '&:active': {
                  transform: 'scale(0.95) translateZ(0)',
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  transition: 'transform 0.1s ease',
                  opacity: 0.8
                }
              }}
            >
              <MenuIcon sx={{
                fontSize: tabletOpts.iconSize,
                // تحسين الوضوح للشاشات عالية الكثافة
                imageRendering: 'crisp-edges'
              }} />
            </IconButton>
          </Box>

          {/* الوسط - عنوان الصفحة */}
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              flexGrow: 1,
              textAlign: 'center',
              fontSize: { xs: '1rem', sm: '1.2rem', md: '1.3rem', lg: '1.4rem' },
              fontWeight: 600,
              mx: 2
            }}
          >
            {menuItems.find(item => item.id === selectedSection)?.label || 'لوحة التحكم'}
          </Typography>

          {/* الجانب الأيمن - الإشعارات وتسجيل الخروج */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* أيقونة الإشعارات */}
            <IconButton
              color="inherit"
              sx={{
                padding: { xs: '8px', sm: '12px', md: '12px' },
                minWidth: { xs: 44, sm: 48, md: 48 },
                minHeight: { xs: 44, sm: 48, md: 48 },
                // تحسينات اللمس
                touchAction: 'manipulation',
                userSelect: 'none',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  transform: 'scale(1.05)',
                  transition: 'all 0.2s ease'
                },
                '&:active': {
                  transform: 'scale(0.95)',
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  transition: 'transform 0.1s ease'
                }
              }}
            >
              <Badge badgeContent={0} color="error">
                <Notifications sx={{ fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.4rem' } }} />
              </Badge>
            </IconButton>

            {/* أيقونة تسجيل الخروج */}
            <IconButton
              color="inherit"
              {...logoutButtonTouch.buttonProps}
              sx={{
                padding: tabletOpts.padding,
                minWidth: touchTarget,
                minHeight: touchTarget,
                color: '#FFD700',
                // تحسينات اللمس للأجهزة اللوحية
                touchAction: 'manipulation',
                userSelect: 'none',
                cursor: 'pointer',
                // تحسين الأداء
                willChange: 'transform, opacity',
                transform: 'translateZ(0)',
                backfaceVisibility: 'hidden',
                // تحسينات للأجهزة اللمسية
                ...(isTouchDevice && {
                  WebkitTapHighlightColor: 'rgba(255, 215, 0, 0.1)',
                  WebkitTouchCallout: 'none'
                }),
                // تحسين hover للأجهزة التي تدعمها
                ...(hasHover && {
                  '&:hover': {
                    backgroundColor: 'rgba(255, 215, 0, 0.1)',
                    color: '#FFF',
                    transform: 'scale(1.05) translateZ(0)',
                    transition: 'all 0.2s ease'
                  }
                }),
                '&:active': {
                  transform: 'scale(0.95) translateZ(0)',
                  backgroundColor: 'rgba(255, 215, 0, 0.2)',
                  transition: 'transform 0.1s ease',
                  opacity: 0.8
                }
              }}
              title={t('logout') || (language === 'ar' ? 'تسجيل الخروج' : 'Logout')}
            >
              <Logout sx={{
                fontSize: tabletOpts.iconSize,
                // تحسين الوضوح للشاشات عالية الكثافة
                imageRendering: 'crisp-edges'
              }} />
            </IconButton>
          </Box>


        </Toolbar>
      </AppBar>

      {/* القائمة الجانبية - ثابتة على اليمين */}
      <Box
        component="nav"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          height: '100vh',
          position: 'fixed',
          top: 0,
          right: 0,
          zIndex: (theme) => theme.zIndex.drawer + 1,
          display: { xs: 'none', md: 'block' }, // إظهار على الأجهزة اللوحية والشاشات الكبيرة
          // تحسينات للأجهزة اللوحية
          touchAction: 'pan-y',
          WebkitOverflowScrolling: 'touch',
          // تحسين الأداء
          willChange: 'transform',
          transform: 'translateZ(0)',
          backfaceVisibility: 'hidden'
        }}
      >
        {/* Drawer للأجهزة المحمولة والأجهزة اللوحية */}
        <Drawer
          variant="temporary"
          anchor="right"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' }, // فقط للأجهزة المحمولة
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: language === 'ar' ? 'rtl' : 'ltr',
              // تحسينات للأجهزة اللوحية واللمس
              touchAction: 'pan-y',
              WebkitOverflowScrolling: 'touch',
              overscrollBehavior: 'contain',
              // تحسين الأداء على الأجهزة اللوحية
              transform: 'translateZ(0)',
              backfaceVisibility: 'hidden',
              // تحسين حجم النقر للأجهزة اللوحية
              '& .MuiListItem-root': {
                minHeight: { xs: 48, sm: 56, md: 48 },
                '& .MuiListItemIcon-root': {
                  minWidth: { xs: 40, sm: 48, md: 40 }
                },
                '& .MuiListItemText-primary': {
                  fontSize: { xs: '0.9rem', sm: '1rem', md: '0.95rem' }
                }
              },
              // تحسين الأزرار للمس
              '& .MuiButton-root': {
                minHeight: { xs: 44, sm: 48, md: 44 },
                fontSize: { xs: '0.85rem', sm: '0.95rem', md: '0.9rem' }
              }
            },
          }}
        >
          {drawer}
        </Drawer>

        {/* لوحة التحكم الثابتة - للأجهزة اللوحية والشاشات الكبيرة */}
        <Box
          sx={{
            display: { xs: 'none', md: 'block' }, // إظهار على الأجهزة اللوحية والشاشات الكبيرة
            width: '100%',
            height: '100vh',
            backgroundColor: 'white',
            borderLeft: '1px solid #e0e0e0',
            direction: language === 'ar' ? 'rtl' : 'ltr',
            overflow: 'auto',
            boxShadow: '-2px 0 10px rgba(0,0,0,0.1)',
            // تحسينات للأجهزة اللوحية
            touchAction: 'pan-y',
            WebkitOverflowScrolling: 'touch',
            overscrollBehavior: 'contain',
            // تحسين الأداء
            transform: 'translateZ(0)',
            backfaceVisibility: 'hidden',
            // تحسين حجم النقر للأجهزة اللوحية
            '& .MuiListItem-root': {
              minHeight: { xs: 48, sm: 56, md: 52, lg: 48 },
              padding: { xs: '8px 16px', sm: '12px 20px', md: '10px 16px' },
              '& .MuiListItemIcon-root': {
                minWidth: { xs: 40, sm: 48, md: 44, lg: 40 },
                '& .MuiSvgIcon-root': {
                  fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.3rem', lg: '1.2rem' }
                }
              },
              '& .MuiListItemText-primary': {
                fontSize: { xs: '0.9rem', sm: '1rem', md: '0.95rem', lg: '0.9rem' },
                fontWeight: 500
              },
              // تحسين hover للأجهزة اللوحية
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 255, 0.08)',
                transform: 'translateX(-2px)',
                transition: 'all 0.2s ease'
              },
              // تحسين active state للمس
              '&:active': {
                backgroundColor: 'rgba(0, 0, 255, 0.12)',
                transform: 'scale(0.98)'
              }
            },
            // تحسين الأزرار للمس
            '& .MuiButton-root': {
              minHeight: { xs: 44, sm: 48, md: 46, lg: 44 },
              fontSize: { xs: '0.85rem', sm: '0.95rem', md: '0.9rem', lg: '0.85rem' },
              padding: { xs: '8px 16px', sm: '10px 20px', md: '9px 18px' },
              borderRadius: { xs: 6, sm: 8, md: 7 },
              // تحسين اللمس
              '&:active': {
                transform: 'scale(0.96)',
                transition: 'transform 0.1s ease'
              }
            },
            // تحسين العناوين
            '& .MuiTypography-h6': {
              fontSize: { xs: '1rem', sm: '1.1rem', md: '1.05rem', lg: '1rem' },
              padding: { xs: '12px 16px', sm: '16px 20px', md: '14px 16px' }
            }
          }}
        >
          {drawer}
        </Box>
      </Box>

      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          backgroundColor: '#f8f9fa',
          direction: language === 'ar' ? 'rtl' : 'ltr',
          pt: `${appBarHeight + 16}px`, // تحديث للتناسب مع الشريط العلوي الديناميكي
          pb: 2,
          overflow: 'auto',
          position: 'relative',
          mr: { xs: 0, md: `${drawerWidth}px` }, // مساحة للوحة التحكم على اليمين للأجهزة اللوحية والشاشات الكبيرة
          // تحسينات للأجهزة اللوحية
          touchAction: 'manipulation',
          WebkitOverflowScrolling: 'touch',
          overscrollBehavior: 'contain',
          // تحسين الأداء
          willChange: 'auto',
          transform: 'translateZ(0)',
          backfaceVisibility: 'hidden'
        }}
      >
        <Container
          maxWidth={false}
          sx={{
            py: 3,
            px: {
              xs: 2,
              sm: 3,
              md: isSmallTablet ? 3 : isLargeTablet ? 4 : 4
            },
            width: '100%',
            maxWidth: 'none !important',
            margin: 0,
            '& > *': {
              width: '100%'
            },
            // تحسينات للأجهزة اللوحية
            touchAction: 'manipulation',
            // تحسين الأداء
            willChange: 'auto',
            transform: 'translateZ(0)',
            backfaceVisibility: 'hidden'
          }}
        >
          {renderContent()}
        </Container>
      </Box>
    </Box>
  );
};

export default AdminDashboard;
