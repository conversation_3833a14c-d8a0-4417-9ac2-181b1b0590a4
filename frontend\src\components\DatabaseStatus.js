import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import { CheckCircle, Error, Warning } from '@mui/icons-material';

// استيراد خدمات قواعد البيانات
import { testSupabaseConnection } from '../supabase/config';
import { db } from '../firebase/config';
import { collection, getDocs, limit, query } from 'firebase/firestore';

const DatabaseStatus = () => {
  const [status, setStatus] = useState({
    firebase: { connected: false, loading: true, error: null },
    supabase: { connected: false, loading: true, error: null }
  });

  useEffect(() => {
    checkDatabaseConnections();
  }, []);

  const checkDatabaseConnections = async () => {
    // اختبار Firebase
    try {
      console.log('🔄 اختبار اتصال Firebase...');
      const testQuery = query(collection(db, 'users'), limit(1));
      await getDocs(testQuery);
      
      setStatus(prev => ({
        ...prev,
        firebase: { connected: true, loading: false, error: null }
      }));
      console.log('✅ Firebase متصل');
    } catch (firebaseError) {
      console.error('❌ خطأ في Firebase:', firebaseError);
      setStatus(prev => ({
        ...prev,
        firebase: { connected: false, loading: false, error: firebaseError.message }
      }));
    }

    // اختبار Supabase
    try {
      console.log('🔄 اختبار اتصال Supabase...');
      const isConnected = await testSupabaseConnection();
      
      setStatus(prev => ({
        ...prev,
        supabase: { connected: isConnected, loading: false, error: isConnected ? null : 'فشل الاتصال' }
      }));
      
      if (isConnected) {
        console.log('✅ Supabase متصل');
      } else {
        console.warn('⚠️ Supabase غير متصل');
      }
    } catch (supabaseError) {
      console.error('❌ خطأ في Supabase:', supabaseError);
      setStatus(prev => ({
        ...prev,
        supabase: { connected: false, loading: false, error: supabaseError.message }
      }));
    }
  };

  const getStatusIcon = (dbStatus) => {
    if (dbStatus.loading) {
      return <CircularProgress size={20} />;
    }
    if (dbStatus.connected) {
      return <CheckCircle color="success" />;
    }
    return <Error color="error" />;
  };

  const getStatusColor = (dbStatus) => {
    if (dbStatus.loading) return 'default';
    if (dbStatus.connected) return 'success';
    return 'error';
  };

  const getStatusText = (dbStatus) => {
    if (dbStatus.loading) return 'جاري الاختبار...';
    if (dbStatus.connected) return 'متصل';
    return 'غير متصل';
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        حالة قواعد البيانات
      </Typography>
      
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            {getStatusIcon(status.firebase)}
            <Typography variant="h6">Firebase</Typography>
            <Chip 
              label={getStatusText(status.firebase)}
              color={getStatusColor(status.firebase)}
              size="small"
            />
          </Box>
          
          {status.firebase.error && (
            <Alert severity="error" sx={{ mb: 1 }}>
              خطأ Firebase: {status.firebase.error}
            </Alert>
          )}
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {getStatusIcon(status.supabase)}
            <Typography variant="h6">Supabase</Typography>
            <Chip 
              label={getStatusText(status.supabase)}
              color={getStatusColor(status.supabase)}
              size="small"
            />
          </Box>
          
          {status.supabase.error && (
            <Alert severity="error" sx={{ mt: 1 }}>
              خطأ Supabase: {status.supabase.error}
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* تحذير إذا كانت إحدى قواعد البيانات غير متصلة */}
      {(!status.firebase.connected || !status.supabase.connected) && 
       !status.firebase.loading && !status.supabase.loading && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body2">
            ⚠️ بعض قواعد البيانات غير متصلة. قد تواجه مشاكل في بعض الوظائف.
          </Typography>
        </Alert>
      )}

      {/* رسالة نجاح إذا كانت جميع قواعد البيانات متصلة */}
      {status.firebase.connected && status.supabase.connected && 
       !status.firebase.loading && !status.supabase.loading && (
        <Alert severity="success">
          <Typography variant="body2">
            ✅ جميع قواعد البيانات متصلة بنجاح!
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default DatabaseStatus;
