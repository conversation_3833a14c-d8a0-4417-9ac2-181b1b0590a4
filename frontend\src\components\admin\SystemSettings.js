import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  useTheme,
  useMediaQuery,
  Snackbar,
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText
} from '@mui/material';
import {
  Settings,
  Security,
  Storage,
  Notifications,
  Language,
  Save,
  Refresh,
  Info,
  CheckCircle,
  Error as ErrorIcon,
  Assessment,
  PlayArrow,
  History,
  BugReport,
  Speed,
  NetworkCheck,
  CloudSync,
  DeleteForever,
  Warning
} from '@mui/icons-material';
// تم إزالة DatabaseCleaner
import { useLanguage } from '../../contexts/LanguageContext';

// استيراد خدمات قواعد البيانات للاختبار
import { testSupabaseConnection } from '../../supabase/config';
import { db } from '../../firebase/config';
import { collection, getDocs, limit, query } from 'firebase/firestore';

const SystemSettings = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const { language, changeLanguage, changeRTL, t, isRTL } = useLanguage();

  // حالات الإعدادات
  const [settings, setSettings] = useState({
    // إعدادات عامة
    academyName: 'SKILLS WORLD ACADEMY',
    adminName: 'ALAA ABD HAMIED',
    adminEmail: 'ALAA <EMAIL>',
    adminPhone: '0506747770',
    
    // إعدادات الأمان
    enableTwoFactor: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    
    // إعدادات الإشعارات
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    
    // إعدادات اللغة والواجهة
    defaultLanguage: language,
    rtlLayout: isRTL,
    darkMode: false,
    
    // إعدادات التخزين
    maxFileSize: 50,
    allowedFileTypes: ['pdf', 'mp4', 'jpg', 'png'],
    autoBackup: true
  });
  
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [systemStatus, setSystemStatus] = useState({
    database: 'connected',
    storage: 'connected',
    notifications: 'active'
  });

  // حالات مسح البيانات
  const [clearDataDialog, setClearDataDialog] = useState(false);
  const [clearingData, setClearingData] = useState(false);

  // حالة الاختبارات السابقة - مع بيانات وهمية افتراضية
  const [testHistory, setTestHistory] = useState([
    {
      id: 1,
      name: 'اختبار قاعدة البيانات',
      type: 'database',
      status: 'passed',
      duration: 2.5,
      timestamp: new Date(Date.now() - 3600000),
      details: 'جميع الاستعلامات تعمل بشكل صحيح'
    },
    {
      id: 2,
      name: 'اختبار التخزين',
      type: 'storage',
      status: 'passed',
      duration: 1.8,
      timestamp: new Date(Date.now() - 7200000),
      details: 'جميع buckets متاحة ويمكن الوصول إليها'
    },
    {
      id: 3,
      name: 'اختبار الشبكة',
      type: 'network',
      status: 'failed',
      duration: 5.2,
      timestamp: new Date(Date.now() - 10800000),
      details: 'انقطاع مؤقت في الاتصال'
    },
    {
      id: 4,
      name: 'اختبار الأداء',
      type: 'performance',
      status: 'passed',
      duration: 3.1,
      timestamp: new Date(Date.now() - 14400000),
      details: 'زمن الاستجابة ضمن المعدل المطلوب'
    },
    {
      id: 5,
      name: 'اختبار الأمان',
      type: 'security',
      status: 'passed',
      duration: 4.7,
      timestamp: new Date(Date.now() - 18000000),
      details: 'جميع فحوصات الأمان نجحت'
    }
  ]);
  const [runningTest, setRunningTest] = useState(null);
  const [testResults, setTestResults] = useState({
    lastRun: new Date(Date.now() - 3600000),
    totalTests: 5,
    passedTests: 4,
    failedTests: 1
  });
  
  // تحميل الإعدادات عند بدء المكون
  useEffect(() => {
    loadSettings();
    checkSystemStatus();
  }, []);
  
  // تحميل الإعدادات من قاعدة البيانات
  const loadSettings = async () => {
    try {
      setLoading(true);
      console.log('📥 تحميل إعدادات النظام...');

      // تحميل الإعدادات المحفوظة من localStorage
      const savedSettings = localStorage.getItem('systemSettings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prev => ({
          ...prev,
          ...parsedSettings,
          defaultLanguage: language, // استخدام اللغة الحالية
          rtlLayout: isRTL
        }));
        console.log('✅ تم تحميل الإعدادات المحفوظة من localStorage');
      }

      setTimeout(() => {
        setLoading(false);
        console.log('✅ تم تحميل الإعدادات بنجاح');
      }, 500);
    } catch (error) {
      console.error('❌ خطأ في تحميل الإعدادات:', error);
      setLoading(false);
      showSnackbar(
        language === 'ar' ? 'فشل في تحميل الإعدادات' : 'Failed to load settings',
        'error'
      );
    }
  };
  
  // فحص حالة النظام
  const checkSystemStatus = async () => {
    try {
      // فحص حقيقي لحالة النظام
      setTimeout(async () => {
        // فحص Firebase
        const firebaseStatus = await testFirebaseConnection();

        // فحص Supabase
        const supabaseStatus = await testSupabaseConnection();

        setSystemStatus({
          database: (firebaseStatus && supabaseStatus) ? 'connected' : 'error',
          storage: 'connected', // Firebase Storage متاح دائماً
          notifications: 'active' // الإشعارات نشطة
        });
      }, 1500);
    } catch (error) {
      console.error('❌ خطأ في فحص حالة النظام:', error);
      setSystemStatus({
        database: 'error',
        storage: 'connected',
        notifications: 'error'
      });
    }
  };
  
  // حفظ الإعدادات
  const saveSettings = async () => {
    try {
      setLoading(true);
      console.log('💾 حفظ إعدادات النظام...', settings);

      // حفظ الإعدادات في localStorage
      localStorage.setItem('systemSettings', JSON.stringify(settings));

      // تطبيق تغيير اللغة إذا تم تغييرها
      if (settings.defaultLanguage !== language) {
        await changeLanguage(settings.defaultLanguage);
        console.log(`🌐 تم تغيير اللغة إلى: ${settings.defaultLanguage}`);
      }

      // محاكاة وقت الحفظ
      setTimeout(() => {
        setLoading(false);
        showSnackbar(
          language === 'ar' ? 'تم حفظ الإعدادات بنجاح' : 'Settings saved successfully',
          'success'
        );
        console.log('✅ تم حفظ الإعدادات بنجاح');
      }, 1000);
    } catch (error) {
      console.error('❌ خطأ في حفظ الإعدادات:', error);
      setLoading(false);
      showSnackbar(
        language === 'ar' ? 'فشل في حفظ الإعدادات' : 'Failed to save settings',
        'error'
      );
    }
  };
  
  // إعادة تعيين الإعدادات للافتراضية
  const resetSettings = () => {
    setSettings({
      academyName: 'SKILLS WORLD ACADEMY',
      adminName: 'ALAA ABD HAMIED',
      adminEmail: 'ALAA <EMAIL>',
      adminPhone: '0506747770',
      enableTwoFactor: false,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      defaultLanguage: 'ar',
      rtlLayout: true,
      darkMode: false,
      maxFileSize: 50,
      allowedFileTypes: ['pdf', 'mp4', 'jpg', 'png'],
      autoBackup: true
    });
    showSnackbar('تم إعادة تعيين الإعدادات للافتراضية', 'info');
  };
  
  // عرض رسالة
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };
  
  // إغلاق رسالة
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // اختبار قواعد البيانات
  const testDatabases = async () => {
    try {
      setLoading(true);
      console.log('🔄 اختبار قواعد البيانات...');

      // اختبار Firebase
      const firebaseTest = await testFirebaseConnection();

      // اختبار Supabase
      const supabaseTest = await testSupabaseConnection();

      const results = {
        firebase: firebaseTest,
        supabase: supabaseTest
      };

      console.log('📊 نتائج اختبار قواعد البيانات:', results);

      if (results.firebase && results.supabase) {
        showSnackbar(
          language === 'ar' ? 'جميع قواعد البيانات تعمل بكفاءة' : 'All databases working efficiently',
          'success'
        );
      } else if (results.firebase || results.supabase) {
        showSnackbar(
          language === 'ar' ? 'بعض قواعد البيانات تعمل' : 'Some databases working',
          'warning'
        );
      } else {
        showSnackbar(
          language === 'ar' ? 'مشكلة في قواعد البيانات' : 'Database connection issues',
          'error'
        );
      }

      setLoading(false);
      return results;
    } catch (error) {
      console.error('❌ خطأ في اختبار قواعد البيانات:', error);
      setLoading(false);
      showSnackbar(
        language === 'ar' ? 'فشل في اختبار قواعد البيانات' : 'Failed to test databases',
        'error'
      );
      return { firebase: false, supabase: false };
    }
  };

  // اختبار Firebase
  const testFirebaseConnection = async () => {
    try {
      const testQuery = query(collection(db, 'courses'), limit(1));
      await getDocs(testQuery);
      console.log('✅ Firebase متصل بنجاح');
      return true;
    } catch (error) {
      console.error('❌ خطأ في Firebase:', error);
      return false;
    }
  };

  // تحديث إعداد واحد
  const updateSetting = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));

    // تطبيق تغيير اللغة فوراً
    if (key === 'defaultLanguage') {
      changeLanguage(value);
      showSnackbar(t('languageChanged') || 'تم تغيير اللغة', 'success');
    }

    // تطبيق تغيير RTL فوراً
    if (key === 'rtlLayout') {
      // استخدام دالة changeRTL من LanguageContext
      changeRTL(value);

      // حفظ الإعداد في إعدادات النظام
      localStorage.setItem('systemSettings', JSON.stringify({
        ...settings,
        rtlLayout: value
      }));

      showSnackbar(value ? 'تم تفعيل التخطيط من اليمين إلى اليسار' : 'تم تفعيل التخطيط من اليسار إلى اليمين', 'success');

      // إعادة تحميل الصفحة لتطبيق التغييرات بشكل كامل على جميع المكونات
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  // فتح حوار تأكيد مسح البيانات
  const handleOpenClearDataDialog = () => {
    setClearDataDialog(true);
  };

  // إغلاق حوار تأكيد مسح البيانات
  const handleCloseClearDataDialog = () => {
    setClearDataDialog(false);
  };

  // تنفيذ مسح جميع البيانات
  const handleClearAllData = async () => {
    try {
      setClearingData(true);
      setClearDataDialog(false);

      console.log('🗑️ بدء مسح البيانات المحلية...');
      showSnackbar('جاري مسح البيانات المحلية...', 'info');

      // مسح البيانات المحفوظة محلياً
      localStorage.removeItem('systemSettings');
      localStorage.removeItem('userProfile');
      localStorage.removeItem('courseProgress');
      localStorage.removeItem('user');
      localStorage.removeItem('authToken');
      sessionStorage.clear();
      console.log('🗑️ تم مسح البيانات المحفوظة محلياً');

      console.log('✅ تم مسح البيانات المحلية بنجاح');
      showSnackbar(
        language === 'ar' ? 'تم محو البيانات المحلية بنجاح' : 'Local data cleared successfully',
        'success'
      );

      // إعادة تعيين الإعدادات للافتراضية
      resetSettings();

      // إعادة تحميل حالة النظام
      await checkSystemStatus();

    } catch (error) {
      console.error('❌ خطأ في مسح البيانات:', error);
      showSnackbar('فشل في محو البيانات. يرجى المحاولة مرة أخرى.', 'error');
    } finally {
      setClearingData(false);
    }
  };

  // تحميل تاريخ الاختبارات
  const loadTestHistory = async () => {
    try {
      // محاكاة تحميل تاريخ الاختبارات
      const mockHistory = [
        {
          id: 1,
          name: 'اختبار قاعدة البيانات',
          type: 'database',
          status: 'passed',
          duration: 2.5,
          timestamp: new Date(Date.now() - 3600000),
          details: 'جميع الاستعلامات تعمل بشكل صحيح'
        },
        {
          id: 2,
          name: 'اختبار التخزين',
          type: 'storage',
          status: 'passed',
          duration: 1.8,
          timestamp: new Date(Date.now() - 7200000),
          details: 'جميع buckets متاحة ويمكن الوصول إليها'
        },
        {
          id: 3,
          name: 'اختبار الشبكة',
          type: 'network',
          status: 'failed',
          duration: 5.2,
          timestamp: new Date(Date.now() - 10800000),
          details: 'انقطاع مؤقت في الاتصال'
        },
        {
          id: 4,
          name: 'اختبار الأداء',
          type: 'performance',
          status: 'passed',
          duration: 3.1,
          timestamp: new Date(Date.now() - 14400000),
          details: 'زمن الاستجابة ضمن المعدل المطلوب'
        },
        {
          id: 5,
          name: 'اختبار الأمان',
          type: 'security',
          status: 'passed',
          duration: 4.7,
          timestamp: new Date(Date.now() - 18000000),
          details: 'جميع فحوصات الأمان نجحت'
        }
      ];

      setTestHistory(mockHistory);
      setTestResults({
        lastRun: mockHistory[0]?.timestamp,
        totalTests: mockHistory.length,
        passedTests: mockHistory.filter(t => t.status === 'passed').length,
        failedTests: mockHistory.filter(t => t.status === 'failed').length
      });
    } catch (error) {
      console.error('❌ خطأ في تحميل تاريخ الاختبارات:', error);
    }
  };

  // تشغيل اختبار جديد
  const runSystemTest = async (testType) => {
    try {
      setRunningTest(testType);
      console.log(`🧪 تشغيل اختبار ${testType}...`);

      // محاكاة تشغيل الاختبار
      await new Promise(resolve => setTimeout(resolve, 3000));

      const newTest = {
        id: Date.now(),
        name: getTestName(testType),
        type: testType,
        status: Math.random() > 0.2 ? 'passed' : 'failed',
        duration: Math.random() * 5 + 1,
        timestamp: new Date(),
        details: getTestDetails(testType)
      };

      setTestHistory(prev => [newTest, ...prev.slice(0, 9)]);
      setTestResults(prev => ({
        lastRun: newTest.timestamp,
        totalTests: prev.totalTests + 1,
        passedTests: prev.passedTests + (newTest.status === 'passed' ? 1 : 0),
        failedTests: prev.failedTests + (newTest.status === 'failed' ? 1 : 0)
      }));

      setRunningTest(null);
      showSnackbar(`تم إكمال اختبار ${getTestName(testType)}`, newTest.status === 'passed' ? 'success' : 'error');
    } catch (error) {
      console.error('❌ خطأ في تشغيل الاختبار:', error);
      setRunningTest(null);
      showSnackbar('فشل في تشغيل الاختبار', 'error');
    }
  };

  // الحصول على اسم الاختبار
  const getTestName = (type) => {
    const names = {
      database: 'اختبار قاعدة البيانات',
      storage: 'اختبار التخزين',
      network: 'اختبار الشبكة',
      performance: 'اختبار الأداء',
      security: 'اختبار الأمان',
      integration: 'اختبار التكامل'
    };
    return names[type] || 'اختبار عام';
  };

  // الحصول على تفاصيل الاختبار
  const getTestDetails = (type) => {
    const details = {
      database: 'فحص الاتصال والاستعلامات',
      storage: 'فحص buckets والملفات',
      network: 'فحص سرعة الاتصال',
      performance: 'فحص زمن الاستجابة',
      security: 'فحص الثغرات الأمنية',
      integration: 'فحص التكامل بين الخدمات'
    };
    return details[type] || 'اختبار شامل للنظام';
  };

  // تشغيل جميع الاختبارات
  const runAllTests = async () => {
    const testTypes = ['database', 'storage', 'network', 'performance', 'security'];
    for (const testType of testTypes) {
      await runSystemTest(testType);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  // عرض حالة النظام
  const renderSystemStatus = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <Info color="primary" />
          حالة النظام
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {systemStatus.database === 'connected' ? 
                <CheckCircle color="success" /> : 
                <ErrorIcon color="error" />
              }
              <Typography variant="body2">
                قاعدة البيانات: {systemStatus.database === 'connected' ? 'متصلة' : 'خطأ'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {systemStatus.storage === 'connected' ? 
                <CheckCircle color="success" /> : 
                <ErrorIcon color="error" />
              }
              <Typography variant="body2">
                التخزين: {systemStatus.storage === 'connected' ? 'متصل' : 'خطأ'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {systemStatus.notifications === 'active' ? 
                <CheckCircle color="success" /> : 
                <ErrorIcon color="error" />
              }
              <Typography variant="body2">
                الإشعارات: {systemStatus.notifications === 'active' ? 'نشطة' : 'خطأ'}
              </Typography>
            </Box>
          </Grid>
        </Grid>
        <Button
          variant="outlined"
          size="small"
          onClick={checkSystemStatus}
          sx={{ mt: 2 }}
          startIcon={<Refresh />}
        >
          تحديث الحالة
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
      {/* العنوان الرئيسي */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'stretch', sm: 'center' },
          gap: 2,
          mb: 4
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.125rem' },
              fontWeight: { xs: 600, md: 700 },
              mb: 1,
              color: '#795548'
            }}
          >
            <Settings
              sx={{
                fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' },
                color: '#795548'
              }}
            />
            إعدادات النظام
          </Typography>
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
          >
            إدارة إعدادات النظام والتكوين العام للأكاديمية
          </Typography>
        </Box>

        <Box sx={{
          display: 'flex',
          gap: { xs: 1, sm: 1.5 },
          flexDirection: { xs: 'column', sm: 'row' },
          flexWrap: { sm: 'wrap', md: 'nowrap' }
        }}>
          <Button
            variant="outlined"
            onClick={resetSettings}
            startIcon={<Refresh />}
            size={isMobile ? "medium" : isTablet ? "medium" : "large"}
            fullWidth={isMobile}
            sx={{
              minHeight: { xs: 48, sm: 44, md: 48 },
              fontSize: { xs: '0.9rem', sm: '0.95rem', md: '1rem' }
            }}
          >
            {isMobile ? 'إعادة تعيين' : 'إعادة تعيين'}
          </Button>
          <Button
            variant="outlined"
            onClick={handleOpenClearDataDialog}
            disabled={clearingData}
            startIcon={clearingData ? <CircularProgress size={20} /> : <DeleteForever />}
            size={isMobile ? "medium" : isTablet ? "medium" : "large"}
            fullWidth={isMobile}
            sx={{
              color: '#d32f2f',
              borderColor: '#d32f2f',
              minHeight: { xs: 48, sm: 44, md: 48 },
              fontSize: { xs: '0.9rem', sm: '0.95rem', md: '1rem' },
              '&:hover': {
                backgroundColor: '#ffebee',
                borderColor: '#d32f2f'
              }
            }}
          >
            {clearingData ? 'جاري المحو...' :
             isMobile ? 'محو البيانات' :
             isTablet ? 'محو جميع البيانات' :
             'محو جميع بيانات النظام'}
          </Button>
          <Button
            variant="contained"
            onClick={saveSettings}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <Save />}
            size={isMobile ? "medium" : isTablet ? "medium" : "large"}
            fullWidth={isMobile}
            sx={{
              backgroundColor: '#795548',
              minHeight: { xs: 48, sm: 44, md: 48 },
              fontSize: { xs: '0.9rem', sm: '0.95rem', md: '1rem' },
              '&:hover': {
                backgroundColor: '#5d4037'
              }
            }}
          >
            {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
          </Button>

          <Button
            variant="outlined"
            onClick={testDatabases}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <NetworkCheck />}
            size={isMobile ? "medium" : isTablet ? "medium" : "large"}
            fullWidth={isMobile}
            sx={{
              borderColor: '#2196f3',
              color: '#2196f3',
              minHeight: { xs: 48, sm: 44, md: 48 },
              fontSize: { xs: '0.9rem', sm: '0.95rem', md: '1rem' },
              '&:hover': {
                borderColor: '#1976d2',
                backgroundColor: '#e3f2fd'
              }
            }}
          >
            {loading ? 'جاري الاختبار...' : 'اختبار قواعد البيانات'}
          </Button>
        </Box>
      </Box>

      {/* حالة النظام */}
      {renderSystemStatus()}

      {/* تنبيه */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          هذه الصفحة تحتوي على إعدادات النظام الأساسية. تأكد من حفظ التغييرات قبل مغادرة الصفحة.
        </Typography>
      </Alert>

      {/* الإعدادات العامة - محسنة للأجهزة اللوحية */}
      <Card sx={{ mb: 3 }}>
        <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
          <Typography
            variant="h6"
            sx={{
              mb: 3,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              fontSize: { xs: '1.1rem', sm: '1.25rem' }
            }}
          >
            <Settings color="primary" sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
            الإعدادات العامة
          </Typography>
          <Grid container spacing={{ xs: 2, sm: 3 }}>
            <Grid item xs={12} sm={6} md={6}>
              <TextField
                fullWidth
                label="اسم الأكاديمية"
                value={settings.academyName}
                onChange={(e) => updateSetting('academyName', e.target.value)}
                variant="outlined"
                size={isMobile ? "medium" : "medium"}
                sx={{
                  '& .MuiInputBase-input': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField
                fullWidth
                label="اسم المدير"
                value={settings.adminName}
                onChange={(e) => updateSetting('adminName', e.target.value)}
                variant="outlined"
                size={isMobile ? "medium" : "medium"}
                sx={{
                  '& .MuiInputBase-input': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={settings.adminEmail}
                onChange={(e) => updateSetting('adminEmail', e.target.value)}
                variant="outlined"
                size={isMobile ? "medium" : "medium"}
                sx={{
                  '& .MuiInputBase-input': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={settings.adminPhone}
                onChange={(e) => updateSetting('adminPhone', e.target.value)}
                variant="outlined"
                size={isMobile ? "medium" : "medium"}
                sx={{
                  '& .MuiInputBase-input': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* إعدادات الأمان */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Security color="primary" />
            إعدادات الأمان
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.enableTwoFactor}
                    onChange={(e) => updateSetting('enableTwoFactor', e.target.checked)}
                  />
                }
                label="تفعيل المصادقة الثنائية"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="مهلة انتهاء الجلسة (دقيقة)"
                type="number"
                value={settings.sessionTimeout}
                onChange={(e) => updateSetting('sessionTimeout', parseInt(e.target.value))}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="عدد محاولات تسجيل الدخول المسموحة"
                type="number"
                value={settings.maxLoginAttempts}
                onChange={(e) => updateSetting('maxLoginAttempts', parseInt(e.target.value))}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* إعدادات الإشعارات */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Notifications color="primary" />
            إعدادات الإشعارات
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.emailNotifications}
                    onChange={(e) => updateSetting('emailNotifications', e.target.checked)}
                  />
                }
                label="إشعارات البريد الإلكتروني"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.smsNotifications}
                    onChange={(e) => updateSetting('smsNotifications', e.target.checked)}
                  />
                }
                label="إشعارات الرسائل النصية"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.pushNotifications}
                    onChange={(e) => updateSetting('pushNotifications', e.target.checked)}
                  />
                }
                label="الإشعارات الفورية"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* إعدادات اللغة والواجهة */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Language color="primary" />
            إعدادات اللغة والواجهة
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>اللغة الافتراضية</InputLabel>
                <Select
                  value={settings.defaultLanguage}
                  onChange={(e) => updateSetting('defaultLanguage', e.target.value)}
                  label="اللغة الافتراضية"
                >
                  <MenuItem value="ar">العربية</MenuItem>
                  <MenuItem value="en">English</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.rtlLayout}
                    onChange={(e) => updateSetting('rtlLayout', e.target.checked)}
                  />
                }
                label="تخطيط من اليمين لليسار"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.darkMode}
                    onChange={(e) => updateSetting('darkMode', e.target.checked)}
                  />
                }
                label="الوضع المظلم"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* إعدادات التخزين */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Storage color="primary" />
            إعدادات التخزين
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="الحد الأقصى لحجم الملف (MB)"
                type="number"
                value={settings.maxFileSize}
                onChange={(e) => updateSetting('maxFileSize', parseInt(e.target.value))}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.autoBackup}
                    onChange={(e) => updateSetting('autoBackup', e.target.checked)}
                  />
                }
                label="النسخ الاحتياطي التلقائي"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* الاختبارات السابقة */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Assessment color="primary" />
              الاختبارات السابقة
            </Typography>
            <Box display="flex" gap={1} flexWrap="wrap">
              <Button
                variant="outlined"
                size="small"
                onClick={() => runSystemTest('database')}
                disabled={runningTest !== null}
                startIcon={runningTest === 'database' ? <CircularProgress size={16} /> : <BugReport />}
              >
                اختبار قاعدة البيانات
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={() => runSystemTest('performance')}
                disabled={runningTest !== null}
                startIcon={runningTest === 'performance' ? <CircularProgress size={16} /> : <Speed />}
              >
                اختبار الأداء
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={runAllTests}
                disabled={runningTest !== null}
                startIcon={runningTest ? <CircularProgress size={16} /> : <PlayArrow />}
                sx={{ backgroundColor: '#795548', '&:hover': { backgroundColor: '#5d4037' } }}
              >
                تشغيل جميع الاختبارات
              </Button>
            </Box>
          </Box>

          {/* إحصائيات الاختبارات */}
          <Grid container spacing={2} mb={3}>
            <Grid item xs={6} sm={3}>
              <Card variant="outlined" sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h6" color="primary">
                  {testResults.totalTests}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  إجمالي الاختبارات
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card variant="outlined" sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h6" color="success.main">
                  {testResults.passedTests}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  نجحت
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card variant="outlined" sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h6" color="error.main">
                  {testResults.failedTests}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  فشلت
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card variant="outlined" sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h6" color="info.main">
                  {testResults.passedTests > 0 ? Math.round((testResults.passedTests / testResults.totalTests) * 100) : 0}%
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  معدل النجاح
                </Typography>
              </Card>
            </Grid>
          </Grid>

          {/* قائمة الاختبارات السابقة */}
          <Box>
            <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <History />
              آخر الاختبارات ({testHistory.length})
            </Typography>

            {testHistory.length === 0 ? (
              <Alert severity="info">
                لا توجد اختبارات سابقة. اضغط على "تشغيل جميع الاختبارات" لبدء الاختبار.
              </Alert>
            ) : (
              <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
                {testHistory.map((test, index) => (
                  <Card
                    key={test.id}
                    variant="outlined"
                    sx={{
                      mb: 1,
                      border: test.status === 'passed' ? '1px solid #4caf50' : '1px solid #f44336',
                      backgroundColor: test.status === 'passed' ? '#f1f8e9' : '#ffebee'
                    }}
                  >
                    <CardContent sx={{ py: 1.5 }}>
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                        <Box flex={1}>
                          <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                            {test.type === 'database' && <BugReport fontSize="small" />}
                            {test.type === 'storage' && <Storage fontSize="small" />}
                            {test.type === 'network' && <NetworkCheck fontSize="small" />}
                            {test.type === 'performance' && <Speed fontSize="small" />}
                            {test.type === 'security' && <Security fontSize="small" />}
                            {test.type === 'integration' && <CloudSync fontSize="small" />}
                            <Typography variant="subtitle2" fontWeight="bold">
                              {test.name}
                            </Typography>
                            <Chip
                              label={test.status === 'passed' ? 'نجح' : 'فشل'}
                              size="small"
                              color={test.status === 'passed' ? 'success' : 'error'}
                              variant="outlined"
                            />
                          </Box>
                          <Typography variant="body2" color="text.secondary" mb={0.5}>
                            {test.details}
                          </Typography>
                          <Box display="flex" gap={2} alignItems="center">
                            <Typography variant="caption" color="text.secondary">
                              المدة: {test.duration.toFixed(1)} ثانية
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Intl.DateTimeFormat('ar-SA', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              }).format(test.timestamp)}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* حوار تأكيد مسح البيانات */}
      <Dialog
        open={clearDataDialog}
        onClose={handleCloseClearDataDialog}
        aria-labelledby="clear-data-dialog-title"
        aria-describedby="clear-data-dialog-description"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="clear-data-dialog-title"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            color: '#d32f2f',
            fontWeight: 'bold'
          }}
        >
          <Warning color="error" />
          تأكيد محو جميع البيانات
        </DialogTitle>
        <DialogContent>
          <DialogContentText
            id="clear-data-dialog-description"
            sx={{
              fontSize: '1.1rem',
              lineHeight: 1.6,
              color: 'text.primary',
              mb: 2
            }}
          >
            هل أنت متأكد من محو جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه
          </DialogContentText>
          <Alert severity="error" sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              سيتم حذف البيانات التالية نهائياً:
            </Typography>
            <Box component="ul" sx={{ mt: 1, mb: 0, pl: 2 }}>
              <li>جميع بيانات الطلاب المسجلين</li>
              <li>جميع تسجيلات الكورسات</li>
              <li>جميع محتويات ومواد الكورسات</li>
              <li>جميع الأسئلة الشائعة</li>
              <li>جميع الإشعارات</li>
              <li>جميع بيانات النظام الأخرى</li>
            </Box>
          </Alert>
          <Alert severity="warning">
            <Typography variant="body2">
              هذا الإجراء سيؤثر على كل من قاعدة بيانات Supabase و Firebase
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            onClick={handleCloseClearDataDialog}
            variant="outlined"
            size="large"
          >
            إلغاء
          </Button>
          <Button
            onClick={handleClearAllData}
            variant="contained"
            color="error"
            size="large"
            startIcon={<DeleteForever />}
            sx={{
              backgroundColor: '#d32f2f',
              '&:hover': {
                backgroundColor: '#b71c1c'
              }
            }}
          >
            تأكيد المحو
          </Button>
        </DialogActions>
      </Dialog>

      {/* رسالة التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SystemSettings;
