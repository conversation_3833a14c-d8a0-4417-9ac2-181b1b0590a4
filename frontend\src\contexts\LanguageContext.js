import React, { createContext, useContext, useState, useEffect } from 'react';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import rtlPlugin from 'stylis-plugin-rtl';
import { prefixer } from 'stylis';

// إنشاء Context للغة
const LanguageContext = createContext();

// Hook لاستخدام اللغة
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// إعدادات الـ Cache للـ RTL
const cacheRtl = createCache({
  key: 'muirtl',
  stylisPlugins: [prefixer, rtlPlugin],
});

const cacheLtr = createCache({
  key: 'muiltr',
});

// ترجمات النظام
const translations = {
  ar: {
    // Navigation
    dashboard: 'لوحة التحكم',
    courses: 'الكورسات',
    students: 'الطلاب',
    enrollments: 'التسجيلات',
    settings: 'الإعدادات',
    profile: 'الملف الشخصي',
    logout: 'تسجيل الخروج',
    
    // Common
    save: 'حفظ',
    cancel: 'إلغاء',
    delete: 'حذف',
    edit: 'تعديل',
    add: 'إضافة',
    search: 'بحث',
    loading: 'جاري التحميل...',
    success: 'تم بنجاح',
    error: 'حدث خطأ',
    warning: 'تحذير',
    info: 'معلومات',
    
    // Login
    adminLogin: 'تسجيل دخول المدير',
    studentLogin: 'تسجيل دخول الطالب',
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    studentCode: 'كود الطالب (6 أرقام)',
    login: 'دخول',
    loggingIn: 'جاري تسجيل الدخول...',
    admin: 'المدير',
    student: 'الطالب',
    studentCodeDesc: 'أدخل الكود المكون من 6 أرقام الذي حصلت عليه من المدير',
    platformDesc: '🌟 منصة التعلم والتطوير المهني 🌟',
    enterEmail: 'أدخل البريد الإلكتروني',
    enterPassword: 'أدخل كلمة المرور',
    enterStudentCode: 'أدخل كود الطالب المكون من 6 أرقام',
    
    // System Settings
    systemSettings: 'إعدادات النظام',
    generalSettings: 'الإعدادات العامة',
    securitySettings: 'إعدادات الأمان',
    languageSettings: 'إعدادات اللغة والواجهة',
    defaultLanguage: 'اللغة الافتراضية',
    rtlLayout: 'تخطيط من اليمين لليسار',
    darkMode: 'الوضع المظلم',

    // Admin Dashboard
    adminDashboard: 'لوحة التحكم الإدارية',
    welcome: 'مرحباً',
    systemStatus: 'حالة النظام',
    connected: 'متصل',
    disconnected: 'غير متصل',
    initializing: 'جاري التهيئة',
    
    // Course Management
    courseManagement: 'إدارة الكورسات',
    addCourse: 'إضافة كورس',
    editCourse: 'تعديل الكورس',
    courseName: 'اسم الكورس',
    courseDescription: 'وصف الكورس',
    
    // Student Management
    studentManagement: 'إدارة الطلاب',
    addStudent: 'إضافة طالب',
    studentName: 'اسم الطالب',
    studentEmail: 'بريد الطالب',
    
    // Enrollment Management
    enrollmentManagement: 'إدارة التسجيلات',
    enrollStudent: 'تسجيل طالب',
    enrollmentDate: 'تاريخ التسجيل',
    
    // Messages
    dataCleared: 'تم محو جميع البيانات بنجاح',
    settingsSaved: 'تم حفظ الإعدادات بنجاح',
    languageChanged: 'تم تغيير اللغة بنجاح'
  },
  
  en: {
    // Navigation
    dashboard: 'Dashboard',
    courses: 'Courses',
    students: 'Students',
    enrollments: 'Enrollments',
    settings: 'Settings',
    profile: 'Profile',
    logout: 'Logout',
    
    // Common
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information',
    
    // Login
    adminLogin: 'Admin Login',
    studentLogin: 'Student Login',
    email: 'Email Address',
    password: 'Password',
    studentCode: 'Student Code (6 digits)',
    login: 'Login',
    loggingIn: 'Logging in...',
    admin: 'Admin',
    student: 'Student',
    studentCodeDesc: 'Enter the 6-digit code you received from the admin',
    platformDesc: '🌟 Learning & Professional Development Platform 🌟',
    enterEmail: 'Enter email address',
    enterPassword: 'Enter password',
    enterStudentCode: 'Enter 6-digit student code',
    
    // System Settings
    systemSettings: 'System Settings',
    generalSettings: 'General Settings',
    securitySettings: 'Security Settings',
    languageSettings: 'Language & Interface Settings',
    defaultLanguage: 'Default Language',
    rtlLayout: 'Right-to-Left Layout',
    darkMode: 'Dark Mode',

    // Admin Dashboard
    adminDashboard: 'Admin Dashboard',
    welcome: 'Welcome',
    systemStatus: 'System Status',
    connected: 'Connected',
    disconnected: 'Disconnected',
    initializing: 'Initializing',
    
    // Course Management
    courseManagement: 'Course Management',
    addCourse: 'Add Course',
    editCourse: 'Edit Course',
    courseName: 'Course Name',
    courseDescription: 'Course Description',
    
    // Student Management
    studentManagement: 'Student Management',
    addStudent: 'Add Student',
    studentName: 'Student Name',
    studentEmail: 'Student Email',
    
    // Enrollment Management
    enrollmentManagement: 'Enrollment Management',
    enrollStudent: 'Enroll Student',
    enrollmentDate: 'Enrollment Date',
    
    // Messages
    dataCleared: 'All data cleared successfully',
    settingsSaved: 'Settings saved successfully',
    languageChanged: 'Language changed successfully'
  }
};

// مزود اللغة
export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('ar');
  const [isRTL, setIsRTL] = useState(true);

  // تحميل اللغة المحفوظة عند بدء التطبيق
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'ar';
    const savedRTL = localStorage.getItem('isRTL') === 'true';
    
    setLanguage(savedLanguage);
    setIsRTL(savedRTL);
    
    // تطبيق اتجاه النص على الـ body
    document.body.dir = savedRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = savedLanguage;
  }, []);

  // تغيير اللغة
  const changeLanguage = (newLanguage) => {
    const newIsRTL = newLanguage === 'ar';

    setLanguage(newLanguage);
    setIsRTL(newIsRTL);

    // حفظ في التخزين المحلي
    localStorage.setItem('language', newLanguage);
    localStorage.setItem('isRTL', newIsRTL.toString());

    // تطبيق اتجاه النص
    document.body.dir = newIsRTL ? 'rtl' : 'ltr';
    document.documentElement.dir = newIsRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = newLanguage;

    // إضافة فئة CSS للجسم
    document.body.className = document.body.className.replace(/\b(rtl|ltr)\b/g, '');
    document.body.classList.add(newIsRTL ? 'rtl' : 'ltr');
  };

  // تغيير اتجاه RTL فقط (بدون تغيير اللغة)
  const changeRTL = (newIsRTL) => {
    setIsRTL(newIsRTL);

    // حفظ في التخزين المحلي
    localStorage.setItem('isRTL', newIsRTL.toString());

    // تطبيق اتجاه النص
    document.body.dir = newIsRTL ? 'rtl' : 'ltr';
    document.documentElement.dir = newIsRTL ? 'rtl' : 'ltr';

    // إضافة فئة CSS للجسم
    document.body.className = document.body.className.replace(/\b(rtl|ltr)\b/g, '');
    document.body.classList.add(newIsRTL ? 'rtl' : 'ltr');
  };

  // الحصول على الترجمة
  const t = (key) => {
    return translations[language]?.[key] || key;
  };

  // إنشاء الثيم حسب اللغة مع تحسينات للأجهزة اللوحية
  const theme = createTheme({
    direction: isRTL ? 'rtl' : 'ltr',

    // تخصيص breakpoints للأجهزة اللوحية
    breakpoints: {
      values: {
        xs: 0,      // الهواتف الصغيرة
        sm: 600,    // الهواتف الكبيرة
        md: 900,    // الأجهزة اللوحية الصغيرة
        lg: 1200,   // الأجهزة اللوحية الكبيرة
        xl: 1536,   // الشاشات الكبيرة
        // إضافة breakpoints مخصصة للأجهزة اللوحية
        tablet: 768,     // الأجهزة اللوحية العادية
        tabletLg: 1024,  // الأجهزة اللوحية الكبيرة
      },
    },

    typography: {
      fontFamily: isRTL
        ? '"Cairo", "Roboto", "Helvetica", "Arial", sans-serif'
        : '"Roboto", "Helvetica", "Arial", sans-serif',

      // تحسين أحجام الخطوط للأجهزة اللوحية
      h1: {
        fontSize: '2.5rem',
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '2.2rem',
        },
      },
      h2: {
        fontSize: '2rem',
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '1.8rem',
        },
      },
      h3: {
        fontSize: '1.75rem',
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '1.6rem',
        },
      },
      h4: {
        fontSize: '1.5rem',
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '1.4rem',
        },
      },
      h5: {
        fontSize: '1.25rem',
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '1.2rem',
        },
      },
      h6: {
        fontSize: '1.1rem',
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '1.1rem',
        },
      },
      body1: {
        fontSize: '1rem',
        lineHeight: 1.6,
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '1rem',
          lineHeight: 1.5,
        },
      },
      body2: {
        fontSize: '0.875rem',
        lineHeight: 1.5,
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '0.9rem',
          lineHeight: 1.4,
        },
      },
      button: {
        fontSize: '0.875rem',
        fontWeight: 600,
        '@media (min-width:768px) and (max-width:1024px)': {
          fontSize: '1rem',
          fontWeight: 500,
        },
      },
    },

    palette: {
      primary: {
        main: '#0000FF',
        light: '#4169E1',
        dark: '#0000CC',
        contrastText: '#ffffff',
      },
      secondary: {
        main: '#FFD700',
        light: '#FFED4E',
        dark: '#FFC107',
        contrastText: '#000000',
      },
      background: {
        default: '#f5f5f5',
        paper: '#ffffff',
      },
      text: {
        primary: '#333333',
        secondary: '#666666',
      },
    },

    // تحسين المكونات للأجهزة اللوحية
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            minHeight: 44,
            minWidth: 44,
            padding: '8px 16px',
            touchAction: 'manipulation',
            cursor: 'pointer',
            transition: 'all 0.2s ease',

            // تحسينات للأجهزة اللوحية
            '@media (min-width:768px) and (max-width:1024px)': {
              minHeight: 48,
              minWidth: 48,
              padding: '12px 20px',
              fontSize: '1rem',
            },

            // تحسينات للأجهزة اللوحية الكبيرة
            '@media (min-width:1024px) and (max-width:1366px)': {
              minHeight: 52,
              minWidth: 52,
              padding: '14px 24px',
              fontSize: '1.1rem',
            },

            // تحسين hover للأجهزة التي تدعمها
            '@media (hover: hover) and (pointer: fine)': {
              '&:hover': {
                transform: 'scale(1.02)',
                transition: 'transform 0.2s ease',
              },
            },

            // تحسين active state
            '&:active': {
              transform: 'scale(0.98)',
              transition: 'transform 0.1s ease',
              opacity: 0.8,
            },
          },
        },
      },

      MuiIconButton: {
        styleOverrides: {
          root: {
            minHeight: 44,
            minWidth: 44,
            padding: 12,
            touchAction: 'manipulation',
            cursor: 'pointer',
            transition: 'all 0.2s ease',

            // تحسينات للأجهزة اللوحية
            '@media (min-width:768px) and (max-width:1024px)': {
              minHeight: 48,
              minWidth: 48,
              padding: 14,
            },

            // تحسينات للأجهزة اللوحية الكبيرة
            '@media (min-width:1024px) and (max-width:1366px)': {
              minHeight: 52,
              minWidth: 52,
              padding: 16,
            },

            // تحسين hover
            '@media (hover: hover) and (pointer: fine)': {
              '&:hover': {
                transform: 'scale(1.02)',
                transition: 'transform 0.2s ease',
              },
            },

            // تحسين active state
            '&:active': {
              transform: 'scale(0.98)',
              transition: 'transform 0.1s ease',
              opacity: 0.8,
            },
          },
        },
      },

      MuiListItemButton: {
        styleOverrides: {
          root: {
            minHeight: 48,
            padding: '8px 16px',
            touchAction: 'manipulation',
            cursor: 'pointer',
            transition: 'all 0.2s ease',

            // تحسينات للأجهزة اللوحية
            '@media (min-width:768px) and (max-width:1024px)': {
              minHeight: 56,
              padding: '12px 20px',
            },

            // تحسينات للأجهزة اللوحية الكبيرة
            '@media (min-width:1024px) and (max-width:1366px)': {
              minHeight: 60,
              padding: '14px 24px',
            },

            // تحسين hover
            '@media (hover: hover) and (pointer: fine)': {
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 255, 0.04)',
                transform: 'translateX(-2px)',
                transition: 'all 0.2s ease',
              },
            },

            // تحسين active state
            '&:active': {
              backgroundColor: 'rgba(0, 0, 255, 0.08)',
              transform: 'scale(0.98)',
              transition: 'transform 0.1s ease',
            },
          },
        },
      },

      MuiDrawer: {
        styleOverrides: {
          paper: {
            // تحسين الأداء
            willChange: 'transform',
            transform: 'translateZ(0)',
            backfaceVisibility: 'hidden',

            // تحسين التمرير
            WebkitOverflowScrolling: 'touch',
            overscrollBehavior: 'contain',

            // تحسينات للأجهزة اللوحية
            '@media (min-width:768px) and (max-width:1024px)': {
              width: 300,
            },

            // تحسينات للأجهزة اللوحية الكبيرة
            '@media (min-width:1024px) and (max-width:1366px)': {
              width: 320,
            },
          },
        },
      },

      MuiAppBar: {
        styleOverrides: {
          root: {
            // تحسين الأداء
            willChange: 'auto',
            transform: 'translateZ(0)',
            backfaceVisibility: 'hidden',

            // تحسينات للأجهزة اللوحية
            '@media (min-width:768px) and (max-width:1024px)': {
              height: 64,
            },

            // تحسينات للأجهزة اللوحية الكبيرة
            '@media (min-width:1024px) and (max-width:1366px)': {
              height: 68,
            },
          },
        },
      },

      MuiToolbar: {
        styleOverrides: {
          root: {
            touchAction: 'manipulation',

            // تحسينات للأجهزة اللوحية
            '@media (min-width:768px) and (max-width:1024px)': {
              minHeight: 64,
              padding: '0 24px',
            },

            // تحسينات للأجهزة اللوحية الكبيرة
            '@media (min-width:1024px) and (max-width:1366px)': {
              minHeight: 68,
              padding: '0 32px',
            },
          },
        },
      },
    },
  });

  const value = {
    language,
    isRTL,
    changeLanguage,
    changeRTL,
    t,
    translations: translations[language]
  };

  return (
    <LanguageContext.Provider value={value}>
      <CacheProvider value={isRTL ? cacheRtl : cacheLtr}>
        <ThemeProvider theme={theme}>
          <div dir={isRTL ? 'rtl' : 'ltr'}>
            {children}
          </div>
        </ThemeProvider>
      </CacheProvider>
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
