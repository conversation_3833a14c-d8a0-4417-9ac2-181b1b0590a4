import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || "AIzaSyCYJMUqUhABuifOwet69hXqaCqemx3LEnM",
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || "marketwise-academy-qhizq.firebaseapp.com",
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || "marketwise-academy-qhizq",
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || "marketwise-academy-qhizq.firebasestorage.app",
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || "986248981376",
  appId: process.env.REACT_APP_FIREBASE_APP_ID || "1:986248981376:web:145e0f48d4aea4e08f6f65"
};

// طباعة معلومات التكوين للتشخيص (فقط في التطوير)
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Firebase Configuration:', {
    apiKey: firebaseConfig.apiKey ? '✅ موجود' : '❌ مفقود',
    authDomain: firebaseConfig.authDomain,
    projectId: firebaseConfig.projectId,
    storageBucket: firebaseConfig.storageBucket,
    messagingSenderId: firebaseConfig.messagingSenderId,
    appId: firebaseConfig.appId ? '✅ موجود' : '❌ مفقود',
    environment: process.env.NODE_ENV || 'development'
  });
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);
export const auth = getAuth(app);
export const storage = getStorage(app);

// تعطيل المحاكيات للاتصال بقاعدة البيانات الحقيقية
// Connect to emulators in development
// if (process.env.NODE_ENV === 'development' && !window.location.hostname.includes('web.app')) {
//   try {
//     connectFirestoreEmulator(db, 'localhost', 8080);
//     connectAuthEmulator(auth, 'http://localhost:9099');
//     connectStorageEmulator(storage, 'localhost', 9199);
//   } catch (error) {
//     console.log('Emulators already connected or not available');
//   }
// }

export default app;
