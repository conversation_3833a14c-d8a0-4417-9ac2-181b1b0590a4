import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  setDoc,
  query,
  where,
  limit,
  serverTimestamp
} from 'firebase/firestore';
import { db } from './config';

// ===== خدمة إدارة قاعدة البيانات الرئيسية =====

/**
 * إنشاء هيكل قاعدة البيانات الأساسي
 */
export const initializeDatabase = async () => {
  try {
    console.log('🔧 تهيئة قاعدة البيانات...');

    // تهيئة بسيطة بدون إنشاء بيانات
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    return true;

    // إنشاء الإعدادات العامة
    const settingsRef = doc(db, 'settings', 'general');
    const settingsDoc = await getDoc(settingsRef);

    if (!settingsDoc.exists()) {
      await setDoc(settingsRef, {
        siteName: 'SKILLS WORLD ACADEMY',
        adminName: 'ALAA ABD HAMIED',
        adminEmail: 'ALAA <EMAIL>',
        adminPhone: '0506747770',
        maxStudents: 1000,
        allowRegistration: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      console.log('⚙️ تم إنشاء الإعدادات العامة');
    }

    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    throw error;
  }
};

/**
 * إنشاء كود تسجيل فريد للطالب
 */
export const generateStudentCode = async () => {
  try {
    let isUnique = false;
    let studentCode = '';

    while (!isUnique) {
      // إنشاء كود من 6 أرقام
      studentCode = Math.floor(100000 + Math.random() * 900000).toString();
      
      // التحقق من عدم وجود الكود مسبقاً
      const codeQuery = query(
        collection(db, 'users'),
        where('studentCode', '==', studentCode),
        limit(1)
      );
      const codeSnapshot = await getDocs(codeQuery);
      
      if (codeSnapshot.empty) {
        isUnique = true;
      }
    }

    return studentCode;
  } catch (error) {
    console.error('خطأ في إنشاء كود الطالب:', error);
    throw error;
  }
};

/**
 * إنشاء طالب جديد
 */
export const createStudent = async (studentData) => {
  try {
    const studentCode = await generateStudentCode();
    
    const newStudent = {
      ...studentData,
      studentCode,
      role: 'student',
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, 'users'), newStudent);
    
    // تسجيل النشاط
    await logActivity('admin', 'student_created', {
      studentId: docRef.id,
      studentName: studentData.name,
      studentCode
    });

    return {
      id: docRef.id,
      ...newStudent,
      studentCode
    };
  } catch (error) {
    console.error('خطأ في إنشاء الطالب:', error);
    throw error;
  }
};

/**
 * البحث عن طالب بكود التسجيل
 */
export const findStudentByCode = async (studentCode) => {
  try {
    const studentQuery = query(
      collection(db, 'users'),
      where('studentCode', '==', studentCode),
      where('role', '==', 'student'),
      limit(1)
    );
    
    const snapshot = await getDocs(studentQuery);
    
    if (snapshot.empty) {
      return null;
    }

    const studentDoc = snapshot.docs[0];
    return {
      id: studentDoc.id,
      ...studentDoc.data()
    };
  } catch (error) {
    console.error('خطأ في البحث عن الطالب:', error);
    throw error;
  }
};

/**
 * تسجيل نشاط في النظام
 */
export const logActivity = async (userId, action, details = {}) => {
  try {
    await addDoc(collection(db, 'activities'), {
      userId,
      action,
      details,
      timestamp: serverTimestamp(),
      type: 'system'
    });
  } catch (error) {
    console.error('خطأ في تسجيل النشاط:', error);
    // لا نرمي خطأ هنا لأن تسجيل النشاط ليس حرجاً
  }
};

/**
 * تسجيل طالب في كورس
 */
export const enrollStudentInCourse = async (studentId, courseId) => {
  try {
    // التحقق من عدم وجود تسجيل مسبق
    const existingEnrollment = query(
      collection(db, 'enrollments'),
      where('studentId', '==', studentId),
      where('courseId', '==', courseId),
      limit(1)
    );
    
    const existingSnapshot = await getDocs(existingEnrollment);
    
    if (!existingSnapshot.empty) {
      throw new Error('الطالب مسجل في هذا الكورس مسبقاً');
    }

    // إنشاء تسجيل جديد
    const enrollmentData = {
      studentId,
      courseId,
      enrolledAt: serverTimestamp(),
      progress: 0,
      completedVideos: [],
      lastWatchedVideo: null,
      totalWatchTime: 0
    };

    const docRef = await addDoc(collection(db, 'enrollments'), enrollmentData);
    
    // تسجيل النشاط
    await logActivity(studentId, 'course_enrolled', {
      courseId,
      enrollmentId: docRef.id
    });

    return {
      id: docRef.id,
      ...enrollmentData
    };
  } catch (error) {
    console.error('خطأ في تسجيل الطالب في الكورس:', error);
    throw error;
  }
};

/**
 * الحصول على كورسات الطالب
 */
export const getStudentCourses = async (studentId) => {
  try {
    // جلب التسجيلات
    const enrollmentsQuery = query(
      collection(db, 'enrollments'),
      where('studentId', '==', studentId)
    );
    
    const enrollmentsSnapshot = await getDocs(enrollmentsQuery);
    const enrollments = enrollmentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // جلب تفاصيل الكورسات
    const courses = [];
    for (const enrollment of enrollments) {
      const courseDoc = await getDoc(doc(db, 'courses', enrollment.courseId));
      if (courseDoc.exists()) {
        courses.push({
          ...courseDoc.data(),
          id: courseDoc.id,
          enrollment
        });
      }
    }

    return courses;
  } catch (error) {
    console.error('خطأ في جلب كورسات الطالب:', error);
    throw error;
  }
};

const databaseService = {
  initializeDatabase,
  generateStudentCode,
  createStudent,
  findStudentByCode,
  logActivity,
  enrollStudentInCourse,
  getStudentCourses
};

export default databaseService;
