import { initializeDatabase } from './databaseService';

/**
 * تهيئة التطبيق وقاعدة البيانات
 */
export const initializeApp = async () => {
  try {
    // تهيئة قاعدة البيانات بدون تأخير
    await initializeDatabase();
    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة التطبيق:', error);
    // Continue even if initialization fails
    return false;
  }
};

export default initializeApp;
