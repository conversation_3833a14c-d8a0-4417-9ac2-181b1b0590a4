import { useEffect, useRef, useCallback } from 'react';
import touchHandlers from '../utils/touchHandlers';

/**
 * Hook لاستخدام معالجات اللمس المحسنة
 * Enhanced touch handlers hook
 */
export const useEnhancedTouch = (options = {}) => {
  const elementRef = useRef(null);
  const deviceInfoRef = useRef(touchHandlers.detectDeviceType());
  
  const {
    hapticFeedback = true,
    visualFeedback = true,
    preventDoubleClick = true,
    debounceTime = 300,
    scaleEffect = true,
    autoOptimize = true
  } = options;
  
  // تحديث معلومات الجهاز عند تغيير حجم الشاشة
  useEffect(() => {
    const unsubscribe = touchHandlers.createScreenSizeObserver((newDeviceInfo) => {
      deviceInfoRef.current = newDeviceInfo;
      console.log('📱 تحديث معلومات الجهاز:', newDeviceInfo);
    });
    
    return unsubscribe;
  }, []);
  
  // تحسين العنصر تلقائياً
  useEffect(() => {
    if (autoOptimize && elementRef.current) {
      touchHandlers.optimizePerformance(elementRef.current);
      touchHandlers.enhanceScrolling(elementRef.current);
      
      // إضافة فئات CSS للتحسين
      elementRef.current.classList.add('touch-optimized', 'scroll-optimized', 'text-optimized');
      
      // إضافة فئة للتفاعل المحسن
      if (deviceInfoRef.current.isTouchDevice) {
        elementRef.current.classList.add('enhanced-touch');
      }
      
      if (deviceInfoRef.current.hasHover) {
        elementRef.current.classList.add('enhanced-hover');
      }
      
      elementRef.current.classList.add('enhanced-focus');
    }
  }, [autoOptimize]);
  
  // إنشاء معالج لمس محسن
  const createTouchHandler = useCallback((originalHandler) => {
    return touchHandlers.createEnhancedTouchHandler(originalHandler, {
      hapticFeedback,
      visualFeedback,
      preventDoubleClick,
      debounceTime,
      scaleEffect
    });
  }, [hapticFeedback, visualFeedback, preventDoubleClick, debounceTime, scaleEffect]);
  
  // إضافة معالجات لمس محسنة
  const addTouchHandlers = useCallback((handlers = {}) => {
    if (elementRef.current) {
      touchHandlers.addEnhancedTouchHandlers(elementRef.current, handlers, {
        hapticFeedback,
        visualFeedback,
        preventDoubleClick,
        debounceTime,
        scaleEffect
      });
    }
  }, [hapticFeedback, visualFeedback, preventDoubleClick, debounceTime, scaleEffect]);
  
  // الحصول على معلومات الجهاز الحالية
  const getDeviceInfo = useCallback(() => {
    return deviceInfoRef.current;
  }, []);
  
  // تحديث معلومات الجهاز
  const updateDeviceInfo = useCallback(() => {
    deviceInfoRef.current = touchHandlers.detectDeviceType();
    return deviceInfoRef.current;
  }, []);
  
  return {
    elementRef,
    deviceInfo: deviceInfoRef.current,
    createTouchHandler,
    addTouchHandlers,
    getDeviceInfo,
    updateDeviceInfo,
    // معلومات مفيدة للمكونات
    isTouchDevice: deviceInfoRef.current.isTouchDevice,
    hasHover: deviceInfoRef.current.hasHover,
    isTablet: deviceInfoRef.current.isTablet,
    deviceType: deviceInfoRef.current.deviceType
  };
};

/**
 * Hook لتحسين الأزرار للأجهزة اللوحية
 */
export const useEnhancedButton = (onClick, options = {}) => {
  const { elementRef, createTouchHandler, deviceInfo } = useEnhancedTouch(options);
  
  const enhancedClickHandler = useCallback(
    createTouchHandler(onClick),
    [createTouchHandler, onClick]
  );
  
  // خصائص محسنة للزر
  const buttonProps = {
    ref: elementRef,
    onClick: enhancedClickHandler,
    style: {
      minHeight: deviceInfo.isTouchDevice ? 48 : 44,
      minWidth: deviceInfo.isTouchDevice ? 48 : 44,
      touchAction: 'manipulation',
      cursor: 'pointer',
      userSelect: 'none',
      WebkitUserSelect: 'none',
      WebkitTapHighlightColor: 'transparent',
      WebkitTouchCallout: 'none',
      willChange: 'transform, opacity',
      transform: 'translateZ(0)',
      backfaceVisibility: 'hidden'
    }
  };
  
  return {
    buttonProps,
    deviceInfo,
    enhancedClickHandler
  };
};

/**
 * Hook لتحسين القوائم للأجهزة اللوحية
 */
export const useEnhancedList = (options = {}) => {
  const { elementRef, addTouchHandlers, deviceInfo } = useEnhancedTouch(options);
  
  useEffect(() => {
    if (elementRef.current) {
      // تحسين التمرير للقائمة
      touchHandlers.enhanceScrolling(elementRef.current);
      
      // إضافة فئات CSS
      elementRef.current.classList.add('enhanced-list');
      
      if (deviceInfo.isTouchDevice) {
        elementRef.current.classList.add('touch-list');
      }
    }
  }, [deviceInfo.isTouchDevice]);
  
  // خصائص محسنة للقائمة
  const listProps = {
    ref: elementRef,
    style: {
      touchAction: 'pan-y',
      WebkitOverflowScrolling: 'touch',
      overscrollBehavior: 'contain',
      scrollBehavior: 'smooth'
    }
  };
  
  return {
    listProps,
    deviceInfo,
    addTouchHandlers
  };
};

/**
 * Hook لتحسين عناصر القائمة للأجهزة اللوحية
 */
export const useEnhancedListItem = (onClick, options = {}) => {
  const { elementRef, createTouchHandler, deviceInfo } = useEnhancedTouch(options);
  
  const enhancedClickHandler = useCallback(
    createTouchHandler(onClick),
    [createTouchHandler, onClick]
  );
  
  // خصائص محسنة لعنصر القائمة
  const listItemProps = {
    ref: elementRef,
    onClick: enhancedClickHandler,
    style: {
      minHeight: deviceInfo.isTouchDevice ? 56 : 48,
      touchAction: 'manipulation',
      cursor: 'pointer',
      userSelect: 'none',
      WebkitUserSelect: 'none',
      WebkitTapHighlightColor: 'transparent',
      WebkitTouchCallout: 'none',
      willChange: 'transform, opacity',
      transform: 'translateZ(0)',
      backfaceVisibility: 'hidden'
    }
  };
  
  return {
    listItemProps,
    deviceInfo,
    enhancedClickHandler
  };
};

/**
 * Hook لتحسين الحاويات للأجهزة اللوحية
 */
export const useEnhancedContainer = (options = {}) => {
  const { elementRef, deviceInfo } = useEnhancedTouch(options);
  
  useEffect(() => {
    if (elementRef.current) {
      // تحسين الأداء للحاوية
      touchHandlers.optimizePerformance(elementRef.current);
      touchHandlers.enhanceScrolling(elementRef.current);
      
      // إضافة فئات CSS
      elementRef.current.classList.add('enhanced-container');
      
      if (deviceInfo.isTouchDevice) {
        elementRef.current.classList.add('touch-container');
      }
    }
  }, [deviceInfo.isTouchDevice]);
  
  // خصائص محسنة للحاوية
  const containerProps = {
    ref: elementRef,
    style: {
      touchAction: 'manipulation',
      WebkitOverflowScrolling: 'touch',
      overscrollBehavior: 'contain',
      willChange: 'auto',
      transform: 'translateZ(0)',
      backfaceVisibility: 'hidden'
    }
  };
  
  return {
    containerProps,
    deviceInfo
  };
};

export default {
  useEnhancedTouch,
  useEnhancedButton,
  useEnhancedList,
  useEnhancedListItem,
  useEnhancedContainer
};
