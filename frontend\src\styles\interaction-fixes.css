/* إصلاحات التفاعل للوحة التحكم الإدارية - محدث ومحسن */

/* إصلاح عام للتفاعل */
.admin-dashboard {
  /* إزالة تأخير النقر على iOS */
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;

  /* تحسين الأداء */
  will-change: auto !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;

  /* تحسين التفاعل للأجهزة اللمسية */
  touch-action: manipulation !important;
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;

  /* ضمان التجاوب */
  width: 100% !important;
  max-width: 100vw !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
  position: relative !important;

  /* تحسين الخطوط للأجهزة اللوحية */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

/* إصلاح الأزرار والعناصر التفاعلية - محسن للأجهزة اللوحية */
.admin-dashboard .MuiIconButton-root,
.admin-dashboard .MuiListItemButton-root,
.admin-dashboard .MuiButton-root,
.admin-dashboard .MuiMenuItem-root,
.admin-dashboard .MuiTab-root,
.admin-dashboard .MuiChip-root {
  /* تحسين التفاعل */
  touch-action: manipulation !important;
  user-select: none !important;
  cursor: pointer !important;

  /* إزالة تأخير النقر */
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;

  /* تحسين الاستجابة */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;

  /* ضمان الحد الأدنى للحجم للأجهزة اللمسية */
  min-height: 44px !important;
  min-width: 44px !important;

  /* تحسين الأداء */
  will-change: transform, opacity !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;

  /* تحسين الوضوح */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* تحسين hover للأجهزة التي تدعمها */
@media (hover: hover) and (pointer: fine) {
  .admin-dashboard .MuiIconButton-root:hover,
  .admin-dashboard .MuiListItemButton-root:hover,
  .admin-dashboard .MuiButton-root:hover,
  .admin-dashboard .MuiMenuItem-root:hover,
  .admin-dashboard .MuiTab-root:hover {
    transform: scale(1.02) !important;
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background-color: rgba(0, 0, 255, 0.04) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 255, 0.1) !important;
  }

  .admin-dashboard .MuiListItemButton-root:hover {
    transform: translateX(-2px) scale(1.01) !important;
    box-shadow: 2px 0 12px rgba(0, 0, 255, 0.15) !important;
  }

  .admin-dashboard .MuiChip-root:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 255, 0.2) !important;
  }
}

/* تحسين active state لجميع الأجهزة */
.admin-dashboard .MuiIconButton-root:active,
.admin-dashboard .MuiListItemButton-root:active,
.admin-dashboard .MuiButton-root:active,
.admin-dashboard .MuiMenuItem-root:active,
.admin-dashboard .MuiTab-root:active,
.admin-dashboard .MuiChip-root:active {
  transform: scale(0.96) !important;
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
  opacity: 0.8 !important;
  background-color: rgba(0, 0, 255, 0.08) !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* تحسين focus للوصولية */
.admin-dashboard .MuiIconButton-root:focus,
.admin-dashboard .MuiListItemButton-root:focus,
.admin-dashboard .MuiButton-root:focus,
.admin-dashboard .MuiMenuItem-root:focus,
.admin-dashboard .MuiTab-root:focus {
  outline: 2px solid #0000FF !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(0, 0, 255, 0.2) !important;
  z-index: 1 !important;
}

/* إصلاح القائمة الجانبية */
.admin-dashboard .MuiList-root {
  /* تحسين التمرير */
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

.admin-dashboard .MuiListItem-root {
  /* تحسين التفاعل */
  touch-action: manipulation !important;
  cursor: pointer !important;
}

/* إصلاح الـ Drawer */
.admin-dashboard .MuiDrawer-paper {
  /* تحسين الأداء */
  will-change: transform !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
  
  /* تحسين التمرير */
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* إصلاح الشريط العلوي */
.admin-dashboard .MuiAppBar-root {
  /* تحسين الأداء */
  will-change: auto !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
}

.admin-dashboard .MuiToolbar-root {
  /* تحسين التفاعل */
  touch-action: manipulation !important;
}

/* إصلاحات للأجهزة المحمولة */
@media (max-width: 899px) {
  .admin-dashboard {
    /* تحسين الأداء للأجهزة المحمولة */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
  }
  
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    /* أحجام أكبر للأجهزة المحمولة */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
    
    /* تحسين الاستجابة */
    -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
  }
  
  /* تحسين القائمة للأجهزة المحمولة */
  .admin-dashboard .MuiDrawer-paper {
    width: 280px !important;
    max-width: 85vw !important;
  }
  
  /* تحسين النصوص للأجهزة المحمولة */
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.1rem !important;
    line-height: 1.4 !important;
  }
  
  .admin-dashboard .MuiListItemText-primary {
    font-size: 0.95rem !important;
    line-height: 1.3 !important;
  }
}

/* إصلاحات للأجهزة اللوحية */
@media (min-width: 900px) and (max-width: 1199px) {
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    /* أحجام محسنة للأجهزة اللوحية */
    min-height: 52px !important;
    min-width: 52px !important;
    padding: 14px !important;
  }
  
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.2rem !important;
  }
  
  .admin-dashboard .MuiListItemText-primary {
    font-size: 1rem !important;
  }
}

/* إصلاحات للشاشات الكبيرة */
@media (min-width: 1200px) {
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    /* أحجام مثالية للشاشات الكبيرة */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
  }
  
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.3rem !important;
  }
  
  .admin-dashboard .MuiListItemText-primary {
    font-size: 1rem !important;
  }
}

/* إصلاح مشاكل z-index */
.admin-dashboard .MuiDrawer-root {
  z-index: 1300 !important;
}

.admin-dashboard .MuiAppBar-root {
  z-index: 1200 !important;
}

.admin-dashboard .MuiBackdrop-root {
  z-index: 1250 !important;
}

/* إصلاح التمرير */
.admin-dashboard {
  overflow-x: hidden !important;
}

.admin-dashboard .MuiContainer-root {
  overflow-x: hidden !important;
  width: 100% !important;
  max-width: none !important;
}

/* إصلاح النصوص القابلة للتحديد */
.admin-dashboard .MuiTypography-root,
.admin-dashboard .MuiListItemText-root,
.admin-dashboard input,
.admin-dashboard textarea {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* إصلاح الفوكس */
.admin-dashboard .MuiIconButton-root:focus,
.admin-dashboard .MuiListItemButton-root:focus,
.admin-dashboard .MuiButton-root:focus {
  outline: 2px solid #0000FF !important;
  outline-offset: 2px !important;
}

/* إصلاح الانتقالات */
.admin-dashboard * {
  transition-duration: 0.2s !important;
  transition-timing-function: ease-out !important;
}

/* إصلاح للأجهزة ذات الشاشات اللمسية */
@media (pointer: coarse) {
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    /* أحجام أكبر للأجهزة اللمسية */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 14px !important;
  }
}

/* إصلاح مشاكل الأداء */
.admin-dashboard .performance-critical {
  will-change: transform !important;
  contain: layout style paint !important;
}

/* إصلاح مشاكل الذاكرة */
.admin-dashboard .memory-optimized {
  will-change: auto !important;
  contain: none !important;
}

/* إصلاح للوضع الليلي */
@media (prefers-color-scheme: dark) {
  .admin-dashboard .MuiIconButton-root:active,
  .admin-dashboard .MuiListItemButton-root:active,
  .admin-dashboard .MuiButton-root:active {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}

/* إصلاح للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .admin-dashboard * {
    animation: none !important;
    transition: none !important;
  }
}

/* إجبار التجاوب على جميع الأجهزة */
@media screen {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    min-height: 100vh !important;
    width: 100% !important;
    position: relative !important;
  }

  /* إجبار إخفاء/إظهار العناصر حسب حجم الشاشة */
  @media (max-width: 899px) {
    .admin-dashboard .MuiDrawer-root .MuiDrawer-docked {
      display: none !important;
    }

    .admin-dashboard .MuiAppBar-root {
      width: 100% !important;
      left: 0 !important;
      right: 0 !important;
    }

    .admin-dashboard main {
      margin-right: 0 !important;
      margin-left: 0 !important;
      width: 100% !important;
    }
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    .admin-dashboard .MuiDrawer-root .MuiDrawer-temporary {
      display: none !important;
    }

    .admin-dashboard .MuiAppBar-root {
      width: calc(100% - 300px) !important;
      right: 0 !important;
      left: auto !important;
    }

    .admin-dashboard main {
      margin-right: 300px !important;
      margin-left: 0 !important;
      width: calc(100% - 300px) !important;
    }
  }

  @media (min-width: 1024px) {
    .admin-dashboard .MuiDrawer-root .MuiDrawer-temporary {
      display: none !important;
    }

    .admin-dashboard .MuiAppBar-root {
      width: calc(100% - 320px) !important;
      right: 0 !important;
      left: auto !important;
    }

    .admin-dashboard main {
      margin-right: 320px !important;
      margin-left: 0 !important;
      width: calc(100% - 320px) !important;
    }
  }
}

/* تحسينات خاصة للأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 1366px) {
  .admin-dashboard {
    /* تحسين الخطوط للأجهزة اللوحية */
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiListItemButton-root {
    /* أحجام محسنة للأجهزة اللوحية */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 14px !important;
    font-size: 1rem !important;

    /* تحسين التفاعل */
    -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
    tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
  }

  .admin-dashboard .MuiListItem-root {
    min-height: 56px !important;
    padding: 12px 20px !important;
  }

  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.2rem !important;
    line-height: 1.4 !important;
  }

  .admin-dashboard .MuiListItemText-primary {
    font-size: 1rem !important;
    line-height: 1.3 !important;
  }

  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.4rem !important;
  }
}

/* تحسينات للأجهزة اللوحية الكبيرة */
@media (min-width: 1024px) and (max-width: 1366px) {
  .admin-dashboard {
    font-size: 17px !important;
  }

  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: 52px !important;
    min-width: 52px !important;
    padding: 16px !important;
    font-size: 1.1rem !important;
  }

  .admin-dashboard .MuiListItem-root {
    min-height: 60px !important;
    padding: 14px 24px !important;
  }

  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.3rem !important;
  }

  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.5rem !important;
  }
}

/* تحسينات للأجهزة اللمسية */
@media (pointer: coarse) {
  .admin-dashboard * {
    touch-action: manipulation !important;
  }

  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiMenuItem-root {
    /* أحجام أكبر للأجهزة اللمسية */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 14px !important;

    /* تحسين الاستجابة */
    -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
    tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
  }

  .admin-dashboard .MuiListItem-root {
    min-height: 56px !important;
    padding: 12px 20px !important;
  }

  .admin-dashboard .MuiTypography-root {
    line-height: 1.5 !important;
  }
}

/* تحسينات للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .admin-dashboard .MuiSvgIcon-root {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }

  .admin-dashboard .MuiButton-root {
    border-radius: 6px !important;
  }

  .admin-dashboard .MuiIconButton-root {
    border-radius: 50% !important;
  }

  .admin-dashboard .MuiListItemButton-root {
    border-radius: 8px !important;
  }
}
