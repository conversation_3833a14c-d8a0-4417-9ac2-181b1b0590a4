/* تحسينات شاملة للأجهزة اللوحية - محدث ومحسن */

/* إعدادات أساسية للأجهزة اللوحية */
.admin-dashboard {
  /* تحسين الأداء العام */
  will-change: auto !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;

  /* تحسين التفاعل */
  touch-action: manipulation !important;
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;

  /* ضمان التجاوب الكامل */
  width: 100% !important;
  max-width: 100vw !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* تحسينات للأجهزة اللوحية الصغيرة (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .admin-dashboard {
    font-size: 16px !important;
  }

  .admin-dashboard .MuiAppBar-root {
    height: 64px !important;
    box-shadow: 0 2px 12px rgba(0, 0, 255, 0.15) !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    left: auto !important;
    width: calc(100% - 300px) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: 64px !important;
    padding: 0 24px !important;
    justify-content: space-between !important;
    touch-action: manipulation !important;
  }

  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
  }

  .admin-dashboard .MuiIconButton-root {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 14px !important;
    touch-action: manipulation !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.4rem !important;
  }

  /* تحسين الدرج الجانبي للأجهزة اللوحية */
  .admin-dashboard .MuiDrawer-paper {
    width: 300px !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    height: 100vh !important;
    z-index: 1300 !important;

    /* تحسين الأداء */
    will-change: transform !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;

    /* تحسين التمرير */
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
  }

  /* تحسين المحتوى الرئيسي */
  .admin-dashboard main {
    margin-right: 300px !important;
    margin-left: 0 !important;
    margin-top: 64px !important;
    width: calc(100% - 300px) !important;
    padding: 24px !important;
    min-height: calc(100vh - 64px) !important;
    overflow-x: hidden !important;
  }
}

/* نقاط الكسر للأجهزة اللوحية */
@media (min-width: 900px) and (max-width: 1199px) {

  /* تحسينات عامة للوحة التحكم الإدارية */
  .admin-dashboard-tablet,
  .admin-dashboard {
    /* تحسين الخطوط */
    font-size: 16px !important;
    line-height: 1.5 !important;

    /* تحسين المسافات */
    padding: 16px !important;
    margin: 8px !important;

    /* تحسين التفاعل */
    touch-action: manipulation !important;
    -webkit-overflow-scrolling: touch !important;
  }
  
  /* تحسين الأزرار للأجهزة اللوحية */
  .admin-dashboard-tablet .MuiButton-root {
    min-height: 48px !important;
    min-width: 120px !important;
    padding: 12px 24px !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    margin: 4px !important;
    
    /* تحسين اللمس */
    touch-action: manipulation !important;
    user-select: none !important;
    
    /* تأثيرات التفاعل */
    transition: all 0.2s ease !important;
  }
  
  .admin-dashboard-tablet .MuiButton-root:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 255, 0.2) !important;
  }
  
  .admin-dashboard-tablet .MuiButton-root:active {
    transform: translateY(0) scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
  
  /* تحسين عناصر القائمة */
  .admin-dashboard-tablet .MuiListItem-root {
    min-height: 56px !important;
    padding: 12px 20px !important;
    margin: 2px 8px !important;
    border-radius: 8px !important;
    
    /* تحسين اللمس */
    touch-action: manipulation !important;
    cursor: pointer !important;
  }
  
  .admin-dashboard-tablet .MuiListItem-root:hover {
    background-color: rgba(0, 0, 255, 0.08) !important;
    transform: translateX(-4px) !important;
  }
  
  .admin-dashboard-tablet .MuiListItem-root:active {
    background-color: rgba(0, 0, 255, 0.12) !important;
    transform: scale(0.98) !important;
  }
  
  /* تحسين الأيقونات */
  .admin-dashboard-tablet .MuiSvgIcon-root {
    font-size: 1.5rem !important;
    margin: 4px !important;
  }
  
  .admin-dashboard-tablet .MuiListItemIcon-root {
    min-width: 48px !important;
    margin-right: 12px !important;
  }
  
  /* تحسين النصوص */
  .admin-dashboard-tablet .MuiTypography-h6 {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    margin: 8px 0 !important;
  }
  
  .admin-dashboard-tablet .MuiTypography-body1 {
    font-size: 1rem !important;
    line-height: 1.6 !important;
  }
  
  .admin-dashboard-tablet .MuiTypography-body2 {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
  }
  
  /* تحسين الجداول */
  .admin-dashboard-tablet .MuiTable-root {
    font-size: 0.95rem !important;
  }
  
  .admin-dashboard-tablet .MuiTableCell-root {
    padding: 16px 12px !important;
    font-size: 0.95rem !important;
    border-bottom: 1px solid rgba(224, 224, 224, 0.5) !important;
  }
  
  .admin-dashboard-tablet .MuiTableHead .MuiTableCell-root {
    font-weight: 600 !important;
    background-color: rgba(0, 0, 255, 0.05) !important;
  }
  
  /* تحسين الحقول والمدخلات */
  .admin-dashboard-tablet .MuiTextField-root {
    margin: 8px 0 16px 0 !important;
    width: 100% !important;
  }
  
  .admin-dashboard-tablet .MuiInputBase-root {
    font-size: 1rem !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    min-height: 48px !important;
  }
  
  .admin-dashboard-tablet .MuiInputLabel-root {
    font-size: 1rem !important;
    font-weight: 500 !important;
  }
  
  /* تحسين البطاقات */
  .admin-dashboard-tablet .MuiCard-root {
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    margin: 12px 0 !important;
    padding: 16px !important;
  }
  
  .admin-dashboard-tablet .MuiCardContent-root {
    padding: 20px !important;
  }
  
  /* تحسين الحوارات والنوافذ المنبثقة */
  .admin-dashboard-tablet .MuiDialog-paper {
    margin: 16px !important;
    max-width: calc(100% - 32px) !important;
    border-radius: 12px !important;
  }
  
  .admin-dashboard-tablet .MuiDialogTitle-root {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    padding: 20px 24px 16px !important;
  }
  
  .admin-dashboard-tablet .MuiDialogContent-root {
    padding: 16px 24px !important;
  }
  
  .admin-dashboard-tablet .MuiDialogActions-root {
    padding: 16px 24px 20px !important;
    gap: 12px !important;
  }
  
  /* تحسين الشرائح والتبويبات */
  .admin-dashboard-tablet .MuiTabs-root {
    min-height: 56px !important;
  }
  
  .admin-dashboard-tablet .MuiTab-root {
    min-height: 56px !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    padding: 12px 20px !important;
    min-width: 120px !important;
  }
  
  /* تحسين الشرائح المنزلقة */
  .admin-dashboard-tablet .MuiSlider-root {
    height: 8px !important;
  }
  
  .admin-dashboard-tablet .MuiSlider-thumb {
    width: 24px !important;
    height: 24px !important;
  }
  
  /* تحسين القوائم المنسدلة */
  .admin-dashboard-tablet .MuiSelect-root {
    min-height: 48px !important;
    font-size: 1rem !important;
  }
  
  .admin-dashboard-tablet .MuiMenuItem-root {
    min-height: 48px !important;
    padding: 12px 16px !important;
    font-size: 1rem !important;
  }
  
  /* تحسين الرقائق والعلامات */
  .admin-dashboard-tablet .MuiChip-root {
    height: 36px !important;
    font-size: 0.9rem !important;
    padding: 8px 12px !important;
    margin: 4px !important;
  }
  
  /* تحسين شريط التقدم */
  .admin-dashboard-tablet .MuiLinearProgress-root {
    height: 8px !important;
    border-radius: 4px !important;
  }
  
  .admin-dashboard-tablet .MuiCircularProgress-root {
    margin: 16px !important;
  }
  
  /* تحسين التنبيهات */
  .admin-dashboard-tablet .MuiAlert-root {
    font-size: 1rem !important;
    padding: 16px !important;
    margin: 12px 0 !important;
    border-radius: 8px !important;
  }
  
  /* تحسين الشبكة والتخطيط */
  .admin-dashboard-tablet .MuiGrid-container {
    margin: 0 !important;
    width: 100% !important;
  }
  
  .admin-dashboard-tablet .MuiGrid-item {
    padding: 8px !important;
  }
  
  /* تحسين التمرير */
  .admin-dashboard-tablet {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
    overscroll-behavior: contain !important;
  }
  
  /* تحسين الأداء */
  .admin-dashboard-tablet * {
    will-change: auto !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
  }
  
  /* تحسين الانتقالات */
  .admin-dashboard-tablet .MuiCollapse-root,
  .admin-dashboard-tablet .MuiFade-root,
  .admin-dashboard-tablet .MuiSlide-root {
    transition-duration: 0.3s !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
}

/* تحسينات خاصة للأجهزة اللوحية الكبيرة (1024px - 1366px) */
@media (min-width: 1024px) and (max-width: 1366px) {
  .admin-dashboard {
    font-size: 17px !important;
  }

  .admin-dashboard .MuiAppBar-root {
    height: 68px !important;
    box-shadow: 0 2px 16px rgba(0, 0, 255, 0.18) !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    left: auto !important;
    width: calc(100% - 320px) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: 68px !important;
    padding: 0 32px !important;
    justify-content: space-between !important;
    touch-action: manipulation !important;
  }

  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.3rem !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
  }

  .admin-dashboard .MuiIconButton-root {
    min-height: 52px !important;
    min-width: 52px !important;
    padding: 16px !important;
    touch-action: manipulation !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.5rem !important;
  }

  /* تحسين الدرج الجانبي للأجهزة اللوحية الكبيرة */
  .admin-dashboard .MuiDrawer-paper {
    width: 320px !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    height: 100vh !important;
    z-index: 1300 !important;

    /* تحسين الأداء */
    will-change: transform !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;

    /* تحسين التمرير */
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
  }

  /* تحسين المحتوى الرئيسي */
  .admin-dashboard main {
    margin-right: 320px !important;
    margin-left: 0 !important;
    margin-top: 68px !important;
    width: calc(100% - 320px) !important;
    padding: 32px !important;
    min-height: calc(100vh - 68px) !important;
    overflow-x: hidden !important;
  }

  .admin-dashboard .MuiContainer-root {
    max-width: 100% !important;
    padding: 0 24px !important;
  }

  .admin-dashboard-tablet .MuiDrawer-paper {
    width: 320px !important;
  }
}

/* تحسينات خاصة للأجهزة اللوحية الصغيرة */
@media (min-width: 768px) and (max-width: 1023px) {
  .admin-dashboard-tablet .MuiContainer-root {
    padding: 0 16px !important;
  }
  
  .admin-dashboard-tablet .MuiDrawer-paper {
    width: 280px !important;
  }
  
  .admin-dashboard-tablet .MuiButton-root {
    font-size: 0.95rem !important;
    padding: 10px 20px !important;
  }
}

/* تحسينات RTL للوحة التحكم الإدارية */
.rtl .admin-dashboard {
  direction: rtl !important;
}

.rtl .admin-dashboard .MuiAppBar-root {
  right: 0 !important;
  left: auto !important;
}

.rtl .admin-dashboard .MuiDrawer-paper {
  right: 0 !important;
  left: auto !important;
}

.rtl .admin-dashboard .MuiToolbar-root {
  direction: rtl !important;
  text-align: right !important;
}

.rtl .admin-dashboard .MuiTypography-root {
  text-align: right !important;
}

.rtl .admin-dashboard .MuiListItem-root {
  direction: rtl !important;
  text-align: right !important;
}

.rtl .admin-dashboard .MuiListItemIcon-root {
  margin-right: 0 !important;
  margin-left: 16px !important;
}

.rtl .admin-dashboard .MuiListItemText-root {
  text-align: right !important;
}

/* تحسينات LTR للوحة التحكم الإدارية */
.ltr .admin-dashboard {
  direction: ltr !important;
}

.ltr .admin-dashboard .MuiAppBar-root {
  left: 0 !important;
  right: auto !important;
}

.ltr .admin-dashboard .MuiDrawer-paper {
  left: 0 !important;
  right: auto !important;
}

.ltr .admin-dashboard .MuiToolbar-root {
  direction: ltr !important;
  text-align: left !important;
}

.ltr .admin-dashboard .MuiTypography-root {
  text-align: left !important;
}

.ltr .admin-dashboard .MuiListItem-root {
  direction: ltr !important;
  text-align: left !important;
}

.ltr .admin-dashboard .MuiListItemIcon-root {
  margin-left: 0 !important;
  margin-right: 16px !important;
}

.ltr .admin-dashboard .MuiListItemText-root {
  text-align: left !important;
}

/* تحسينات خاصة للأجهزة اللمسية */
@media (pointer: coarse) {
  .admin-dashboard * {
    touch-action: manipulation !important;
  }

  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiMenuItem-root {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 14px !important;
    cursor: pointer !important;
    -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
    transition: all 0.2s ease !important;
  }

  .admin-dashboard .MuiListItem-root {
    min-height: 56px !important;
    padding: 12px 20px !important;
  }

  .admin-dashboard .MuiTypography-root {
    line-height: 1.5 !important;
  }
}

/* تحسينات hover للأجهزة التي تدعمها */
@media (hover: hover) and (pointer: fine) {
  .admin-dashboard .MuiIconButton-root:hover,
  .admin-dashboard .MuiButton-root:hover,
  .admin-dashboard .MuiListItemButton-root:hover {
    transform: scale(1.02) !important;
    transition: transform 0.2s ease !important;
    background-color: rgba(0, 0, 255, 0.04) !important;
  }

  .admin-dashboard .MuiListItemButton-root:hover {
    transform: translateX(-2px) !important;
    box-shadow: 2px 0 8px rgba(0, 0, 255, 0.1) !important;
  }
}

/* تحسين active state لجميع الأجهزة */
.admin-dashboard .MuiIconButton-root:active,
.admin-dashboard .MuiButton-root:active,
.admin-dashboard .MuiListItemButton-root:active,
.admin-dashboard .MuiMenuItem-root:active {
  transform: scale(0.98) !important;
  transition: transform 0.1s ease !important;
  opacity: 0.8 !important;
  background-color: rgba(0, 0, 255, 0.08) !important;
}

/* تحسين focus للوصولية */
.admin-dashboard .MuiIconButton-root:focus,
.admin-dashboard .MuiButton-root:focus,
.admin-dashboard .MuiListItemButton-root:focus {
  outline: 2px solid #0000FF !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(0, 0, 255, 0.2) !important;
}

/* تحسينات للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .admin-dashboard .MuiSvgIcon-root {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }

  .admin-dashboard .MuiButton-root {
    border-radius: 6px !important;
  }

  .admin-dashboard .MuiIconButton-root {
    border-radius: 50% !important;
  }
}

/* تحسينات للوضع الليلي */
@media (prefers-color-scheme: dark) {
  .admin-dashboard .MuiIconButton-root:active,
  .admin-dashboard .MuiButton-root:active,
  .admin-dashboard .MuiListItemButton-root:active {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .admin-dashboard * {
    animation: none !important;
    transition: none !important;
  }

  .admin-dashboard .MuiIconButton-root:hover,
  .admin-dashboard .MuiButton-root:hover,
  .admin-dashboard .MuiListItemButton-root:hover {
    transform: none !important;
  }
}

/* إجبار التجاوب على جميع الأجهزة */
@media screen {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    min-height: 100vh !important;
    width: 100% !important;
    position: relative !important;
    overflow-x: hidden !important;
  }

  /* إجبار إخفاء/إظهار العناصر حسب حجم الشاشة */
  @media (max-width: 767px) {
    .admin-dashboard .MuiDrawer-root .MuiDrawer-docked {
      display: none !important;
    }

    .admin-dashboard .MuiAppBar-root {
      width: 100% !important;
      left: 0 !important;
      right: 0 !important;
    }

    .admin-dashboard main {
      margin-right: 0 !important;
      margin-left: 0 !important;
      width: 100% !important;
    }
  }

  @media (min-width: 768px) {
    .admin-dashboard .MuiDrawer-root .MuiDrawer-temporary {
      display: none !important;
    }

    .admin-dashboard .MuiAppBar-root {
      width: calc(100% - 300px) !important;
      right: 0 !important;
      left: auto !important;
    }

    .admin-dashboard main {
      margin-right: 300px !important;
      margin-left: 0 !important;
      width: calc(100% - 300px) !important;
    }
  }

  @media (min-width: 1024px) {
    .admin-dashboard .MuiAppBar-root {
      width: calc(100% - 320px) !important;
    }

    .admin-dashboard main {
      margin-right: 320px !important;
      width: calc(100% - 320px) !important;
    }
  }
}
