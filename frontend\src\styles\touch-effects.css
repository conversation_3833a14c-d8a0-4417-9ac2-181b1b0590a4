/* تأثيرات اللمس المحسنة للأجهزة اللوحية */
/* Enhanced Touch Effects for Tablet Devices */

/* تأثيرات اللمس العامة */
.touch-active {
  background-color: rgba(0, 0, 255, 0.08) !important;
  transform: scale(0.96) translateZ(0) !important;
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.touch-start {
  background-color: rgba(0, 0, 255, 0.04) !important;
  transform: scale(0.98) translateZ(0) !important;
  transition: all 0.1s ease !important;
}

.touch-end {
  background-color: rgba(0, 0, 255, 0.12) !important;
  transform: scale(1.02) translateZ(0) !important;
  transition: all 0.2s ease !important;
}

/* تأثيرات خاصة للأزرار */
.MuiButton-root.touch-active,
.MuiIconButton-root.touch-active {
  background-color: rgba(0, 0, 255, 0.1) !important;
  transform: scale(0.94) translateZ(0) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 255, 0.2) !important;
}

.MuiListItemButton-root.touch-active {
  background-color: rgba(0, 0, 255, 0.06) !important;
  transform: translateX(-4px) scale(0.98) translateZ(0) !important;
  box-shadow: 4px 0 12px rgba(0, 0, 255, 0.15) !important;
}

/* تأثيرات للعناصر التفاعلية */
.MuiMenuItem-root.touch-active,
.MuiTab-root.touch-active {
  background-color: rgba(0, 0, 255, 0.08) !important;
  transform: scale(0.96) translateZ(0) !important;
}

.MuiChip-root.touch-active {
  background-color: rgba(0, 0, 255, 0.12) !important;
  transform: scale(0.92) translateZ(0) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 255, 0.25) !important;
}

/* تأثيرات للأجهزة اللوحية الصغيرة */
@media (min-width: 768px) and (max-width: 1023px) {
  .touch-active {
    transform: scale(0.95) translateZ(0) !important;
  }
  
  .MuiButton-root.touch-active,
  .MuiIconButton-root.touch-active {
    transform: scale(0.93) translateZ(0) !important;
    box-shadow: 0 3px 10px rgba(0, 0, 255, 0.25) !important;
  }
  
  .MuiListItemButton-root.touch-active {
    transform: translateX(-6px) scale(0.97) translateZ(0) !important;
    box-shadow: 6px 0 16px rgba(0, 0, 255, 0.2) !important;
  }
}

/* تأثيرات للأجهزة اللوحية الكبيرة */
@media (min-width: 1024px) and (max-width: 1366px) {
  .touch-active {
    transform: scale(0.94) translateZ(0) !important;
  }
  
  .MuiButton-root.touch-active,
  .MuiIconButton-root.touch-active {
    transform: scale(0.92) translateZ(0) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 255, 0.3) !important;
  }
  
  .MuiListItemButton-root.touch-active {
    transform: translateX(-8px) scale(0.96) translateZ(0) !important;
    box-shadow: 8px 0 20px rgba(0, 0, 255, 0.25) !important;
  }
}

/* تأثيرات للأجهزة اللمسية */
@media (pointer: coarse) {
  .touch-active {
    background-color: rgba(0, 0, 255, 0.1) !important;
    transform: scale(0.94) translateZ(0) !important;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
  
  .touch-start {
    background-color: rgba(0, 0, 255, 0.06) !important;
    transform: scale(0.96) translateZ(0) !important;
  }
  
  .touch-end {
    background-color: rgba(0, 0, 255, 0.15) !important;
    transform: scale(1.03) translateZ(0) !important;
  }
}

/* تأثيرات hover للأجهزة التي تدعمها */
@media (hover: hover) and (pointer: fine) {
  .enhanced-hover:hover {
    background-color: rgba(0, 0, 255, 0.04) !important;
    transform: scale(1.02) translateZ(0) !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 8px rgba(0, 0, 255, 0.1) !important;
  }
  
  .MuiButton-root.enhanced-hover:hover,
  .MuiIconButton-root.enhanced-hover:hover {
    background-color: rgba(0, 0, 255, 0.06) !important;
    transform: scale(1.05) translateZ(0) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 255, 0.15) !important;
  }
  
  .MuiListItemButton-root.enhanced-hover:hover {
    background-color: rgba(0, 0, 255, 0.04) !important;
    transform: translateX(-2px) scale(1.01) translateZ(0) !important;
    box-shadow: 2px 0 12px rgba(0, 0, 255, 0.1) !important;
  }
}

/* تأثيرات focus للوصولية */
.enhanced-focus:focus,
.enhanced-focus:focus-visible {
  outline: 2px solid #0000FF !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(0, 0, 255, 0.2) !important;
  z-index: 1 !important;
}

/* تأثيرات للوضع الليلي */
@media (prefers-color-scheme: dark) {
  .touch-active {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
  
  .touch-start {
    background-color: rgba(255, 255, 255, 0.06) !important;
  }
  
  .touch-end {
    background-color: rgba(255, 255, 255, 0.15) !important;
  }
  
  .enhanced-hover:hover {
    background-color: rgba(255, 255, 255, 0.08) !important;
  }
}

/* تأثيرات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .touch-active,
  .touch-start,
  .touch-end,
  .enhanced-hover:hover {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }
}

/* تأثيرات للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .touch-active {
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15) !important;
  }
  
  .MuiButton-root.touch-active,
  .MuiIconButton-root.touch-active {
    box-shadow: 0 2px 8px rgba(0, 0, 255, 0.25) !important;
  }
  
  .MuiListItemButton-root.touch-active {
    box-shadow: 4px 0 12px rgba(0, 0, 255, 0.2) !important;
  }
}

/* تأثيرات خاصة للدرج الجانبي */
.admin-dashboard .MuiDrawer-paper .touch-active {
  background-color: rgba(0, 0, 255, 0.08) !important;
  border-radius: 8px !important;
  margin: 2px 8px !important;
}

.admin-dashboard .MuiDrawer-paper .MuiListItemButton-root.touch-active {
  transform: translateX(-4px) scale(0.98) translateZ(0) !important;
  box-shadow: 4px 0 12px rgba(0, 0, 255, 0.15) !important;
  border-radius: 8px !important;
}

/* تأثيرات خاصة للشريط العلوي */
.admin-dashboard .MuiAppBar-root .touch-active {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 50% !important;
}

.admin-dashboard .MuiAppBar-root .MuiIconButton-root.touch-active {
  background-color: rgba(255, 255, 255, 0.15) !important;
  transform: scale(0.9) translateZ(0) !important;
}

/* تأثيرات للمحتوى الرئيسي */
.admin-dashboard main .touch-active {
  background-color: rgba(0, 0, 255, 0.06) !important;
  border-radius: 6px !important;
}

/* تحسينات عامة للأداء */
.touch-optimized {
  will-change: transform, opacity !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
  contain: layout style paint !important;
}

/* تحسينات للتمرير */
.scroll-optimized {
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
  scroll-behavior: smooth !important;
}

/* تحسينات للنصوص */
.text-optimized {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}
