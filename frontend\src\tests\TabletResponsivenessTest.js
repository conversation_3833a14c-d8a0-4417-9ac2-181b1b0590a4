import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemText,
  Chip,
  Alert,
  Grid,
  Paper,
  IconButton,
  Divider
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Warning,
  Info,
  Refresh,
  TouchApp,
  Tablet,
  Phone,
  Computer
} from '@mui/icons-material';
import { useEnhancedTouch } from '../hooks/useEnhancedTouch';
import touchHandlers from '../utils/touchHandlers';

/**
 * مكون اختبار شامل للاستجابة للأجهزة اللوحية
 * Comprehensive Tablet Responsiveness Test Component
 */
const TabletResponsivenessTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState(null);
  const [touchTestResults, setTouchTestResults] = useState([]);
  
  const enhancedTouch = useEnhancedTouch({
    hapticFeedback: true,
    visualFeedback: true,
    autoOptimize: true
  });

  useEffect(() => {
    // تحديث معلومات الجهاز عند التحميل
    updateDeviceInfo();
    
    // مراقب تغيير حجم الشاشة
    const unsubscribe = touchHandlers.createScreenSizeObserver((newDeviceInfo) => {
      setDeviceInfo(newDeviceInfo);
      console.log('📱 تحديث معلومات الجهاز في الاختبار:', newDeviceInfo);
    });
    
    return unsubscribe;
  }, []);

  const updateDeviceInfo = () => {
    const info = touchHandlers.detectDeviceType();
    setDeviceInfo(info);
    return info;
  };

  // اختبارات الاستجابة الأساسية
  const runBasicResponsivenessTests = () => {
    const tests = [];
    const info = deviceInfo || updateDeviceInfo();
    
    // اختبار viewport
    tests.push({
      name: 'Viewport Configuration',
      status: document.querySelector('meta[name="viewport"]') ? 'pass' : 'fail',
      message: document.querySelector('meta[name="viewport"]') 
        ? 'Viewport meta tag configured correctly'
        : 'Viewport meta tag missing or incorrect'
    });
    
    // اختبار touch support
    tests.push({
      name: 'Touch Support Detection',
      status: info.isTouchDevice ? 'pass' : 'warning',
      message: info.isTouchDevice 
        ? 'Touch device detected'
        : 'Non-touch device detected'
    });
    
    // اختبار breakpoints
    tests.push({
      name: 'Device Type Detection',
      status: 'pass',
      message: `Device type: ${info.deviceType} (${info.width}x${info.height})`
    });
    
    // اختبار CSS classes
    const adminDashboard = document.querySelector('.admin-dashboard');
    tests.push({
      name: 'CSS Classes Applied',
      status: adminDashboard && adminDashboard.classList.contains('touch-optimized') ? 'pass' : 'fail',
      message: adminDashboard 
        ? `Classes: ${Array.from(adminDashboard.classList).join(', ')}`
        : 'Admin dashboard element not found'
    });
    
    return tests;
  };

  // اختبارات التفاعل مع اللمس
  const runTouchInteractionTests = () => {
    const tests = [];
    
    // اختبار الأزرار
    const buttons = document.querySelectorAll('.MuiButton-root, .MuiIconButton-root');
    tests.push({
      name: 'Button Touch Optimization',
      status: buttons.length > 0 ? 'pass' : 'fail',
      message: `Found ${buttons.length} buttons`,
      details: Array.from(buttons).map(btn => ({
        minHeight: getComputedStyle(btn).minHeight,
        minWidth: getComputedStyle(btn).minWidth,
        touchAction: getComputedStyle(btn).touchAction
      }))
    });
    
    // اختبار عناصر القائمة
    const listItems = document.querySelectorAll('.MuiListItemButton-root');
    tests.push({
      name: 'List Item Touch Optimization',
      status: listItems.length > 0 ? 'pass' : 'fail',
      message: `Found ${listItems.length} list items`,
      details: Array.from(listItems).map(item => ({
        minHeight: getComputedStyle(item).minHeight,
        touchAction: getComputedStyle(item).touchAction
      }))
    });
    
    // اختبار الدرج الجانبي
    const drawer = document.querySelector('.MuiDrawer-paper');
    tests.push({
      name: 'Drawer Responsiveness',
      status: drawer ? 'pass' : 'fail',
      message: drawer 
        ? `Drawer width: ${getComputedStyle(drawer).width}`
        : 'Drawer not found'
    });
    
    return tests;
  };

  // اختبارات الأداء
  const runPerformanceTests = () => {
    const tests = [];
    
    // اختبار GPU acceleration
    const elements = document.querySelectorAll('.touch-optimized');
    let gpuAccelerated = 0;
    elements.forEach(el => {
      const transform = getComputedStyle(el).transform;
      if (transform && transform !== 'none') {
        gpuAccelerated++;
      }
    });
    
    tests.push({
      name: 'GPU Acceleration',
      status: gpuAccelerated > 0 ? 'pass' : 'warning',
      message: `${gpuAccelerated}/${elements.length} elements GPU accelerated`
    });
    
    // اختبار scroll optimization
    const scrollContainers = document.querySelectorAll('.scroll-optimized');
    tests.push({
      name: 'Scroll Optimization',
      status: scrollContainers.length > 0 ? 'pass' : 'warning',
      message: `${scrollContainers.length} scroll containers optimized`
    });
    
    return tests;
  };

  // تشغيل جميع الاختبارات
  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setTouchTestResults([]);
    
    try {
      // انتظار قصير للسماح بالتحديث
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const basicTests = runBasicResponsivenessTests();
      const touchTests = runTouchInteractionTests();
      const performanceTests = runPerformanceTests();
      
      setTestResults([
        { category: 'Basic Responsiveness', tests: basicTests },
        { category: 'Touch Interaction', tests: touchTests },
        { category: 'Performance', tests: performanceTests }
      ]);
      
      // اختبار التفاعل مع اللمس
      const touchResults = await testTouchInteractions();
      setTouchTestResults(touchResults);
      
    } catch (error) {
      console.error('خطأ في تشغيل الاختبارات:', error);
    } finally {
      setIsRunning(false);
    }
  };

  // اختبار التفاعل مع اللمس
  const testTouchInteractions = async () => {
    const results = [];
    
    // اختبار النقر على الأزرار
    const testButton = document.querySelector('.MuiButton-root');
    if (testButton) {
      const rect = testButton.getBoundingClientRect();
      results.push({
        name: 'Button Click Area',
        status: (rect.width >= 44 && rect.height >= 44) ? 'pass' : 'fail',
        message: `Button size: ${Math.round(rect.width)}x${Math.round(rect.height)}px`
      });
    }
    
    // اختبار النقر على عناصر القائمة
    const testListItem = document.querySelector('.MuiListItemButton-root');
    if (testListItem) {
      const rect = testListItem.getBoundingClientRect();
      results.push({
        name: 'List Item Click Area',
        status: (rect.height >= 48) ? 'pass' : 'fail',
        message: `List item height: ${Math.round(rect.height)}px`
      });
    }
    
    return results;
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass':
        return <CheckCircle color="success" />;
      case 'fail':
        return <Error color="error" />;
      case 'warning':
        return <Warning color="warning" />;
      default:
        return <Info color="info" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pass':
        return 'success';
      case 'fail':
        return 'error';
      case 'warning':
        return 'warning';
      default:
        return 'info';
    }
  };

  const getDeviceIcon = () => {
    if (!deviceInfo) return <Computer />;
    
    switch (deviceInfo.deviceType) {
      case 'mobile':
        return <Phone />;
      case 'smallTablet':
      case 'largeTablet':
        return <Tablet />;
      default:
        return <Computer />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🧪 اختبار الاستجابة للأجهزة اللوحية
      </Typography>
      
      {/* معلومات الجهاز */}
      {deviceInfo && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              {getDeviceIcon()}
              <Typography variant="h6" sx={{ ml: 1 }}>
                معلومات الجهاز الحالي
              </Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Chip 
                  label={`النوع: ${deviceInfo.deviceType}`} 
                  color="primary" 
                  variant="outlined" 
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Chip 
                  label={`الحجم: ${deviceInfo.width}x${deviceInfo.height}`} 
                  color="secondary" 
                  variant="outlined" 
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Chip 
                  label={deviceInfo.isTouchDevice ? 'يدعم اللمس' : 'لا يدعم اللمس'} 
                  color={deviceInfo.isTouchDevice ? 'success' : 'warning'} 
                  variant="outlined" 
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Chip 
                  label={deviceInfo.hasHover ? 'يدعم Hover' : 'لا يدعم Hover'} 
                  color={deviceInfo.hasHover ? 'success' : 'info'} 
                  variant="outlined" 
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {/* أزرار التحكم */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
        <Button
          variant="contained"
          startIcon={<Refresh />}
          onClick={runAllTests}
          disabled={isRunning}
          size="large"
        >
          {isRunning ? 'جاري التشغيل...' : 'تشغيل جميع الاختبارات'}
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<TouchApp />}
          onClick={updateDeviceInfo}
          size="large"
        >
          تحديث معلومات الجهاز
        </Button>
      </Box>
      
      {/* نتائج الاختبارات */}
      {testResults.length > 0 && (
        <Grid container spacing={3}>
          {testResults.map((category, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {category.category}
                  </Typography>
                  <List dense>
                    {category.tests.map((test, testIndex) => (
                      <ListItem key={testIndex} sx={{ px: 0 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                          {getStatusIcon(test.status)}
                          <Box sx={{ ml: 1, flexGrow: 1 }}>
                            <Typography variant="body2" fontWeight="medium">
                              {test.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {test.message}
                            </Typography>
                          </Box>
                        </Box>
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
      
      {/* نتائج اختبار اللمس */}
      {touchTestResults.length > 0 && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              نتائج اختبار التفاعل مع اللمس
            </Typography>
            <List>
              {touchTestResults.map((result, index) => (
                <ListItem key={index}>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    {getStatusIcon(result.status)}
                    <ListItemText
                      primary={result.name}
                      secondary={result.message}
                      sx={{ ml: 1 }}
                    />
                  </Box>
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      )}
      
      {/* تعليمات الاختبار اليدوي */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📋 تعليمات الاختبار اليدوي
          </Typography>
          <Typography variant="body2" paragraph>
            للتأكد من عمل جميع التحسينات بشكل صحيح، يرجى اختبار ما يلي:
          </Typography>
          <List>
            <ListItem>
              <ListItemText primary="1. اختبر النقر على جميع الأزرار والتأكد من استجابتها" />
            </ListItem>
            <ListItem>
              <ListItemText primary="2. اختبر التمرير في القوائم والمحتوى" />
            </ListItem>
            <ListItem>
              <ListItemText primary="3. اختبر فتح وإغلاق القائمة الجانبية" />
            </ListItem>
            <ListItem>
              <ListItemText primary="4. اختبر تغيير اتجاه الشاشة (إذا كان متاحاً)" />
            </ListItem>
            <ListItem>
              <ListItemText primary="5. تأكد من وضوح النصوص وسهولة القراءة" />
            </ListItem>
          </List>
        </CardContent>
      </Card>
    </Box>
  );
};

export default TabletResponsivenessTest;
