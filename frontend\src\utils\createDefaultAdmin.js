/**
 * إنشاء حساب المدير الافتراضي
 */

import { createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../firebase/config';
import { supabase } from '../supabase/config';

export const createDefaultAdmin = async () => {
  try {
    console.log('👨‍💼 إنشاء حساب المدير الافتراضي...');

    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123';

    // محاولة إنشاء الحساب في Firebase
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
      console.log('✅ تم إنشاء حساب المدير في Firebase:', userCredential.user.uid);

      // إضافة بيانات المدير في Supabase
      try {
        const { data, error } = await supabase
          .from('users')
          .insert([{
            firebase_uid: userCredential.user.uid,
            name: 'علاء عبد الحميد',
            email: adminEmail,
            phone: '0506747770',
            role: 'admin',
            is_active: true,
            created_at: new Date().toISOString()
          }])
          .select()
          .single();

        if (error) {
          console.warn('⚠️ تحذير في إضافة بيانات المدير في Supabase:', error.message);
        } else {
          console.log('✅ تم إضافة بيانات المدير في Supabase:', data.id);
        }
      } catch (supabaseError) {
        console.warn('⚠️ تحذير Supabase:', supabaseError.message);
      }

      return {
        success: true,
        message: 'تم إنشاء حساب المدير بنجاح',
        credentials: {
          email: adminEmail,
          password: adminPassword
        }
      };
    } catch (firebaseError) {
      if (firebaseError.code === 'auth/email-already-in-use') {
        console.log('ℹ️ حساب المدير موجود بالفعل');
        return {
          success: true,
          message: 'حساب المدير موجود بالفعل',
          credentials: {
            email: adminEmail,
            password: adminPassword
          }
        };
      } else {
        throw firebaseError;
      }
    }
  } catch (error) {
    console.error('❌ خطأ في إنشاء حساب المدير:', error);
    return {
      success: false,
      message: 'فشل في إنشاء حساب المدير: ' + error.message
    };
  }
};

// دالة اختبار تسجيل الدخول
export const testAdminLogin = async () => {
  try {
    console.log('🧪 اختبار تسجيل دخول المدير...');
    
    const { hybridAuth } = await import('../services/hybridDatabaseService');
    
    const result = await hybridAuth.signIn('<EMAIL>', 'admin123');
    
    if (result.success) {
      console.log('✅ نجح اختبار تسجيل دخول المدير');
      return {
        success: true,
        message: 'تم اختبار تسجيل دخول المدير بنجاح',
        user: result.user
      };
    } else {
      throw new Error('فشل في تسجيل الدخول');
    }
  } catch (error) {
    console.error('❌ فشل اختبار تسجيل دخول المدير:', error);
    return {
      success: false,
      message: 'فشل في اختبار تسجيل دخول المدير: ' + error.message
    };
  }
};
