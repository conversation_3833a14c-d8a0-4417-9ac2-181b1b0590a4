// معالجات أحداث اللمس المحسنة للأجهزة اللوحية
// Enhanced Touch Event Handlers for Tablet Devices

/**
 * تحديد نوع الجهاز
 */
export const detectDeviceType = () => {
  const width = window.innerWidth;
  const height = window.innerHeight;
  
  // تحديد إذا كان الجهاز يدعم اللمس
  const isTouchDevice = 'ontouchstart' in window || 
                       navigator.maxTouchPoints > 0 || 
                       navigator.msMaxTouchPoints > 0;
  
  // تحديد إذا كان الجهاز يدعم hover
  const hasHover = window.matchMedia('(hover: hover) and (pointer: fine)').matches;
  
  // تحديد نوع الجهاز
  const isMobile = width < 768;
  const isSmallTablet = width >= 768 && width < 1024;
  const isLargeTablet = width >= 1024 && width < 1367;
  const isDesktop = width >= 1367;
  
  return {
    width,
    height,
    isTouchDevice,
    hasHover,
    isMobile,
    isSmallTablet,
    isLargeTablet,
    isDesktop,
    isTablet: isSmallTablet || isLargeTablet,
    deviceType: isMobile ? 'mobile' : 
                isSmallTablet ? 'smallTablet' : 
                isLargeTablet ? 'largeTablet' : 'desktop'
  };
};

/**
 * إنشاء معالج لمس محسن للأزرار
 */
export const createEnhancedTouchHandler = (originalHandler, options = {}) => {
  const {
    hapticFeedback = true,
    visualFeedback = true,
    preventDoubleClick = true,
    debounceTime = 300,
    scaleEffect = true
  } = options;
  
  let lastClickTime = 0;
  let isProcessing = false;
  
  return async (event) => {
    const currentTime = Date.now();
    
    // منع النقر المزدوج
    if (preventDoubleClick && currentTime - lastClickTime < debounceTime) {
      event.preventDefault();
      return;
    }
    
    // منع المعالجة المتعددة
    if (isProcessing) {
      event.preventDefault();
      return;
    }
    
    isProcessing = true;
    lastClickTime = currentTime;
    
    try {
      const target = event.currentTarget;
      const deviceInfo = detectDeviceType();
      
      // تسجيل معلومات التفاعل
      console.log('🔥 Enhanced Touch Event:', {
        type: event.type,
        deviceType: deviceInfo.deviceType,
        isTouchDevice: deviceInfo.isTouchDevice,
        target: target.tagName,
        timestamp: currentTime
      });
      
      // تأثير بصري للمس
      if (visualFeedback && target) {
        // إضافة فئة CSS للتأثير البصري
        target.classList.add('touch-active');
        
        // تأثير التكبير/التصغير
        if (scaleEffect && deviceInfo.isTouchDevice) {
          target.style.transform = 'scale(0.96) translateZ(0)';
          target.style.transition = 'transform 0.1s ease';
        }
        
        // إزالة التأثير بعد فترة قصيرة
        setTimeout(() => {
          target.classList.remove('touch-active');
          if (scaleEffect) {
            target.style.transform = 'scale(1) translateZ(0)';
          }
        }, 150);
      }
      
      // ردود فعل لمسية (إذا كانت مدعومة)
      if (hapticFeedback && navigator.vibrate && deviceInfo.isTouchDevice) {
        // اهتزاز خفيف للتأكيد
        navigator.vibrate(10);
      }
      
      // تنفيذ المعالج الأصلي
      if (originalHandler && typeof originalHandler === 'function') {
        await originalHandler(event);
      }
      
    } catch (error) {
      console.error('❌ خطأ في معالج اللمس المحسن:', error);
    } finally {
      isProcessing = false;
    }
  };
};

/**
 * إضافة معالجات لمس محسنة لعنصر
 */
export const addEnhancedTouchHandlers = (element, handlers = {}, options = {}) => {
  if (!element) return;
  
  const deviceInfo = detectDeviceType();
  const {
    onClick,
    onTouchStart,
    onTouchEnd,
    onMouseEnter,
    onMouseLeave
  } = handlers;
  
  // معالج النقر المحسن
  if (onClick) {
    const enhancedClickHandler = createEnhancedTouchHandler(onClick, options);
    element.addEventListener('click', enhancedClickHandler);
    element.addEventListener('touchend', enhancedClickHandler);
  }
  
  // معالجات اللمس الإضافية
  if (deviceInfo.isTouchDevice) {
    // بداية اللمس
    if (onTouchStart) {
      element.addEventListener('touchstart', (event) => {
        element.classList.add('touch-start');
        onTouchStart(event);
      });
    }
    
    // نهاية اللمس
    if (onTouchEnd) {
      element.addEventListener('touchend', (event) => {
        element.classList.remove('touch-start');
        element.classList.add('touch-end');
        setTimeout(() => {
          element.classList.remove('touch-end');
        }, 200);
        onTouchEnd(event);
      });
    }
    
    // إلغاء اللمس
    element.addEventListener('touchcancel', () => {
      element.classList.remove('touch-start', 'touch-end');
    });
  }
  
  // معالجات الماوس للأجهزة التي تدعم hover
  if (deviceInfo.hasHover) {
    if (onMouseEnter) {
      element.addEventListener('mouseenter', onMouseEnter);
    }
    
    if (onMouseLeave) {
      element.addEventListener('mouseleave', onMouseLeave);
    }
  }
  
  // تحسينات عامة للعنصر
  element.style.touchAction = 'manipulation';
  element.style.userSelect = 'none';
  element.style.webkitUserSelect = 'none';
  element.style.webkitTapHighlightColor = 'transparent';
  element.style.webkitTouchCallout = 'none';
  
  // تحسين الأداء
  element.style.willChange = 'transform, opacity';
  element.style.transform = 'translateZ(0)';
  element.style.backfaceVisibility = 'hidden';
  
  return element;
};

/**
 * تحسين التمرير للأجهزة اللوحية
 */
export const enhanceScrolling = (container) => {
  if (!container) return;
  
  const deviceInfo = detectDeviceType();
  
  if (deviceInfo.isTouchDevice) {
    container.style.webkitOverflowScrolling = 'touch';
    container.style.overscrollBehavior = 'contain';
    container.style.scrollBehavior = 'smooth';
  }
  
  return container;
};

/**
 * تحسين الأداء للأجهزة اللوحية
 */
export const optimizePerformance = (element) => {
  if (!element) return;
  
  // تحسين الأداء
  element.style.willChange = 'auto';
  element.style.transform = 'translateZ(0)';
  element.style.backfaceVisibility = 'hidden';
  element.style.contain = 'layout style paint';
  
  return element;
};

/**
 * مراقب تغيير حجم الشاشة
 */
export const createScreenSizeObserver = (callback) => {
  let currentDeviceInfo = detectDeviceType();
  
  const handleResize = () => {
    const newDeviceInfo = detectDeviceType();
    
    // التحقق من تغيير نوع الجهاز
    if (newDeviceInfo.deviceType !== currentDeviceInfo.deviceType) {
      console.log('📱 تغيير نوع الجهاز:', {
        from: currentDeviceInfo.deviceType,
        to: newDeviceInfo.deviceType,
        width: newDeviceInfo.width,
        height: newDeviceInfo.height
      });
      
      currentDeviceInfo = newDeviceInfo;
      
      if (callback && typeof callback === 'function') {
        callback(newDeviceInfo);
      }
    }
  };
  
  window.addEventListener('resize', handleResize);
  window.addEventListener('orientationchange', handleResize);
  
  // إرجاع دالة لإزالة المراقب
  return () => {
    window.removeEventListener('resize', handleResize);
    window.removeEventListener('orientationchange', handleResize);
  };
};

export default {
  detectDeviceType,
  createEnhancedTouchHandler,
  addEnhancedTouchHandlers,
  enhanceScrolling,
  optimizePerformance,
  createScreenSizeObserver
};
